'use client';

import { useState, useEffect } from 'react';

export default function Header({ currentPage = '' }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [scrolled, setScrolled] = useState(false);

  const handleDropdownToggle = (dropdown) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  const handleMouseEnter = (dropdown) => {
    setActiveDropdown(dropdown);
  };

  const handleMouseLeave = () => {
    setActiveDropdown(null);
  };

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.classList.add('mobile-menu-open');
    } else {
      document.body.classList.remove('mobile-menu-open');
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('mobile-menu-open');
    };
  }, [mobileMenuOpen]);

  return (
    <div className="relative">
      {/* Top Announcement Bar */}
      <div style={{ backgroundColor: '#4caf50' }} className="text-white py-2 px-4">
        <div className="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between gap-2">
          <div className="flex items-center gap-2 text-sm font-medium">
            <i className="fas fa-info-circle text-green-200"></i>
            <span className="hidden sm:inline">🚀 New: Advanced Warehouse Management Solutions Available</span>
            <span className="sm:hidden">🚀 New Solutions Available</span>
          </div>
          <a href="#" className="text-sm font-semibold hover:text-green-200 transition-colors flex items-center gap-1">
            Learn More
            <i className="fas fa-arrow-right text-xs"></i>
          </a>
        </div>
      </div>

      {/* Main Header */}
      <header className={`sticky top-0 z-50 transition-all duration-300 ${scrolled ? 'bg-white shadow-lg border-b border-gray-200' : 'bg-white'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">

            {/* Logo Section */}
            <div className="flex items-center space-x-3">
              <a href="/" className="flex items-center space-x-3">
                <div className="w-10 h-10 lg:w-12 lg:h-12 rounded-lg flex items-center justify-center shadow-sm" style={{ backgroundColor: '#0a1e3a' }}>
                  <i className="fas fa-warehouse text-white text-lg lg:text-xl"></i>
                </div>
                <div>
                  <h1 className="text-xl lg:text-2xl font-bold font-serif" style={{ color: '#0a1e3a' }}>
                    Flexe
                  </h1>
                  <p className="text-xs font-medium hidden sm:block" style={{ color: '#7a7a7a' }}>Warehouse Management</p>
                </div>
              </a>
            </div>

            {/* Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              {/* Solutions Dropdown */}
              <div
                className="relative group"
                onMouseEnter={() => handleMouseEnter('solutions')}
                onMouseLeave={handleMouseLeave}
              >
                <button className={`flex items-center space-x-1 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${currentPage.includes('solutions') ? 'bg-green-50' : 'hover:bg-gray-50'}`} style={{ color: currentPage.includes('solutions') ? '#4caf50' : '#0a1e3a' }}>
                  <span>Solutions</span>
                  <i className="fas fa-chevron-down text-xs transition-transform duration-200 group-hover:rotate-180"></i>
                </button>

                {/* Clean Dropdown */}
                <div className={`absolute top-full left-0 mt-2 w-80 transition-all duration-300 ${activeDropdown === 'solutions' ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible translate-y-2'}`}>
                  <div className="bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden">
                    <div className="p-6">
                      <div className="grid gap-3">
                        <a href="/solutions" className="group flex items-start space-x-3 p-3 rounded-lg hover:bg-green-50 transition-all duration-200">
                          <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1e3a' }}>
                            <i className="fas fa-warehouse text-white text-sm"></i>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold transition-colors" style={{ color: '#0a1e3a' }}>Warehousing</h4>
                            <p className="text-sm mt-1" style={{ color: '#7a7a7a' }}>Flexible infrastructure solutions</p>
                          </div>
                        </a>
                        <a href="/solutions/distribution" className="group flex items-start space-x-3 p-3 rounded-lg hover:bg-green-50 transition-all duration-200">
                          <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1e3a' }}>
                            <i className="fas fa-truck text-white text-sm"></i>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold transition-colors" style={{ color: '#0a1e3a' }}>Distribution</h4>
                            <p className="text-sm mt-1" style={{ color: '#7a7a7a' }}>Rapid retail replenishment</p>
                          </div>
                        </a>
                        <a href="/solutions/fulfillment" className="group flex items-start space-x-3 p-3 rounded-lg hover:bg-green-50 transition-all duration-200">
                          <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#0a1e3a' }}>
                            <i className="fas fa-box text-white text-sm"></i>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold transition-colors" style={{ color: '#0a1e3a' }}>Fulfillment</h4>
                            <p className="text-sm mt-1" style={{ color: '#7a7a7a' }}>Optimized delivery networks</p>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Simple Navigation Links */}
              <a href="/how-it-works" className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${currentPage.includes('how-it-works') ? 'bg-green-50' : 'hover:bg-gray-50'}`} style={{ color: currentPage.includes('how-it-works') ? '#4caf50' : '#0a1e3a' }}>
                How it Works
              </a>

              <a href="/about" className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${currentPage.includes('about') ? 'bg-green-50' : 'hover:bg-gray-50'}`} style={{ color: currentPage.includes('about') ? '#4caf50' : '#0a1e3a' }}>
                About
              </a>

              <a href="/resources" className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${currentPage.includes('resources') ? 'bg-green-50' : 'hover:bg-gray-50'}`} style={{ color: currentPage.includes('resources') ? '#4caf50' : '#0a1e3a' }}>
                Resources
              </a>

              <a href="/contact" className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${currentPage.includes('contact') ? 'bg-green-50' : 'hover:bg-gray-50'}`} style={{ color: currentPage.includes('contact') ? '#4caf50' : '#0a1e3a' }}>
                Contact
              </a>

            </nav>

            {/* Action Buttons */}
            <div className="flex items-center space-x-4">
              <a href="https://app.flexe.com/users/sign_in" target="_blank" rel="noopener noreferrer" className="hidden sm:flex items-center space-x-2 px-4 py-2 text-sm font-medium transition-colors" style={{ color: '#7a7a7a' }}>
                <i className="fas fa-sign-in-alt text-xs"></i>
                <span>Sign In</span>
              </a>

              <a href="/contact" className="hidden sm:flex items-center space-x-2 px-6 py-2.5 text-white text-sm font-semibold rounded-lg transition-all duration-200 shadow-sm hover:shadow-md" style={{ backgroundColor: '#4caf50' }}>
                <span>Get Started</span>
                <i className="fas fa-arrow-right text-xs"></i>
              </a>

              {/* Mobile menu button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="lg:hidden p-2 transition-colors relative z-50"
                style={{ color: '#0a1e3a' }}
              >
                <div className="w-6 h-6 flex flex-col justify-center items-center">
                  <span className={`block w-6 h-0.5 bg-current transition-all duration-300 ${mobileMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}></span>
                  <span className={`block w-6 h-0.5 bg-current transition-all duration-300 mt-1 ${mobileMenuOpen ? 'opacity-0' : ''}`}></span>
                  <span className={`block w-6 h-0.5 bg-current transition-all duration-300 mt-1 ${mobileMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></span>
                </div>
              </button>
            </div>
          </div>

          {/* Modern Mobile Menu */}
          <div className={`lg:hidden fixed inset-0 z-40 transition-all duration-300 ${mobileMenuOpen ? 'visible opacity-100' : 'invisible opacity-0'}`}>
            {/* Backdrop */}
            <div
              className="absolute inset-0 bg-black/50 backdrop-blur-sm"
              onClick={() => setMobileMenuOpen(false)}
            ></div>

            {/* Menu Panel */}
            <div className={`absolute top-0 right-0 h-full w-80 max-w-[90vw] shadow-2xl transform transition-transform duration-300 border-l ${mobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}`} style={{ backgroundColor: '#0a1e3a', borderColor: '#1a2f4a' }}>

              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: '#1a2f4a' }}>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-xl flex items-center justify-center" style={{ backgroundColor: '#f97316' }}>
                    <i className="fas fa-warehouse text-white"></i>
                  </div>
                  <div>
                    <h2 className="font-bold text-white">Flexe</h2>
                    <p className="text-sm" style={{ color: '#7a7a7a' }}>Warehouse Management</p>
                  </div>
                </div>
                <button
                  onClick={() => setMobileMenuOpen(false)}
                  className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
                  style={{ color: '#7a7a7a' }}
                >
                  <i className="fas fa-times text-xl"></i>
                </button>
              </div>

              {/* Navigation */}
              <nav className="flex-1 p-6 overflow-y-auto">
                <div className="space-y-2">
                  <a href="/solutions" className="flex items-center space-x-3 p-3 text-gray-300 rounded-lg transition-all duration-200" style={{ color: '#a0b0c0' }}>
                    <i className="fas fa-warehouse w-5"></i>
                    <span className="font-medium">Solutions</span>
                  </a>
                  <a href="/how-it-works" className="flex items-center space-x-3 p-3 text-gray-300 rounded-lg transition-all duration-200" style={{ color: '#a0b0c0' }}>
                    <i className="fas fa-cogs w-5"></i>
                    <span className="font-medium">How It Works</span>
                  </a>
                  <a href="/about" className="flex items-center space-x-3 p-3 text-gray-300 rounded-lg transition-all duration-200" style={{ color: '#a0b0c0' }}>
                    <i className="fas fa-building w-5"></i>
                    <span className="font-medium">About</span>
                  </a>
                  <a href="/resources" className="flex items-center space-x-3 p-3 text-gray-300 rounded-lg transition-all duration-200" style={{ color: '#a0b0c0' }}>
                    <i className="fas fa-book w-5"></i>
                    <span className="font-medium">Resources</span>
                  </a>
                  <a href="/contact" className="flex items-center space-x-3 p-3 text-gray-300 rounded-lg transition-all duration-200" style={{ color: '#a0b0c0' }}>
                    <i className="fas fa-envelope w-5"></i>
                    <span className="font-medium">Contact</span>
                  </a>
                </div>

                {/* CTA Buttons */}
                <div className="mt-8 space-y-3">
                  <a href="/contact" className="block w-full px-6 py-3 text-white text-center font-semibold rounded-lg transition-all duration-200" style={{ backgroundColor: '#4caf50' }}>
                    Get Started
                  </a>
                  <a href="https://app.flexe.com/users/sign_in" target="_blank" rel="noopener noreferrer" className="block w-full px-6 py-3 border-2 text-center font-medium rounded-lg transition-all duration-200" style={{ borderColor: '#1a2f4a', color: '#a0b0c0' }}>
                    Sign In
                  </a>
                </div>
              </nav>
            </div>
          </div>
        </div>
      </header>
    </div>
  );
}
