export default function Footer() {
  return (
    <footer className="text-white relative" style={{ background: 'linear-gradient(135deg, #0a1e3a 0%, #1a2f4a 50%, #2a4a6a 100%)' }}>
      {/* Professional Background */}
      <div className="absolute inset-0">
        <img
          src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          alt="Warehouse background"
          className="w-full h-full object-cover opacity-5"
        />
        <div className="absolute inset-0" style={{ background: 'linear-gradient(135deg, rgba(10, 30, 58, 0.95) 0%, rgba(26, 47, 74, 0.90) 50%, rgba(42, 74, 106, 0.95) 100%)' }}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">

          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 rounded-lg flex items-center justify-center shadow-lg" style={{ backgroundColor: '#0a1e3a' }}>
                <i className="fas fa-warehouse text-white text-xl"></i>
              </div>
              <div>
                <h3 className="text-2xl font-bold font-serif text-white">Flexe</h3>
                <p className="text-sm" style={{ color: '#a0b0c0' }}>Warehouse Management Solutions</p>
              </div>
            </div>
            <p className="text-gray-300 mb-8 max-w-md leading-relaxed">
              Transform your supply chain with our comprehensive warehouse management platform. Trusted by Fortune 500 companies worldwide for scalable, efficient logistics solutions.
            </p>

            {/* Social Links */}
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300" style={{ backgroundColor: '#1a2f4a' }}>
                <i className="fab fa-linkedin" style={{ color: '#a0b0c0' }}></i>
              </a>
              <a href="#" className="w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300" style={{ backgroundColor: '#1a2f4a' }}>
                <i className="fab fa-twitter" style={{ color: '#a0b0c0' }}></i>
              </a>
              <a href="#" className="w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300" style={{ backgroundColor: '#1a2f4a' }}>
                <i className="fab fa-facebook" style={{ color: '#a0b0c0' }}></i>
              </a>
              <a href="#" className="w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300" style={{ backgroundColor: '#1a2f4a' }}>
                <i className="fab fa-instagram" style={{ color: '#a0b0c0' }}></i>
              </a>
            </div>
          </div>

          {/* Solutions */}
          <div>
            <h4 className="font-semibold text-white mb-6">Solutions</h4>
            <ul className="space-y-3">
              <li><a href="/solutions" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Warehousing
              </a></li>
              <li><a href="/solutions/distribution" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Distribution
              </a></li>
              <li><a href="/solutions/fulfillment" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Fulfillment
              </a></li>
              <li><a href="/solutions/capacity" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Inventory Management
              </a></li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="font-semibold text-white mb-6">Company</h4>
            <ul className="space-y-3">
              <li><a href="/about" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                About Us
              </a></li>
              <li><a href="/careers" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Careers
              </a></li>
              <li><a href="/newsroom" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Newsroom
              </a></li>
              <li><a href="/contact" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Contact
              </a></li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h4 className="font-semibold text-white mb-6">Resources</h4>
            <ul className="space-y-3">
              <li><a href="/resources" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Articles
              </a></li>
              <li><a href="/resources" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Case Studies
              </a></li>
              <li><a href="/resources" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                Webinars
              </a></li>
              <li><a href="/how-it-works" className="transition-colors duration-200" style={{ color: '#a0b0c0' }}>
                How It Works
              </a></li>
            </ul>
          </div>

        </div>

        {/* Newsletter Signup */}
        <div className="mt-12 pt-8 mb-8" style={{ borderTop: '1px solid #1a2f4a' }}>
          <div className="max-w-md mx-auto text-center">
            <h4 className="text-xl font-semibold text-white mb-3">Stay Updated</h4>
            <p className="mb-6" style={{ color: '#a0b0c0' }}>Get the latest insights on warehouse management and supply chain optimization.</p>
            <div className="flex gap-3">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg text-white focus:outline-none focus:ring-2"
                style={{
                  backgroundColor: '#1a2f4a',
                  border: '1px solid #2a4a6a',
                  color: 'white'
                }}
              />
              <button className="px-6 py-3 text-white font-semibold rounded-lg transition-all duration-200" style={{ backgroundColor: '#4caf50' }}>
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-8 flex flex-col md:flex-row justify-between items-center gap-4" style={{ borderTop: '1px solid #1a2f4a' }}>
          <div className="flex items-center space-x-6">
            <p className="text-sm" style={{ color: '#a0b0c0' }}>
              © 2024 Flexe. All rights reserved.
            </p>
            <div className="flex items-center space-x-2 text-sm" style={{ color: '#a0b0c0' }}>
              <i className="fas fa-shield-alt" style={{ color: '#4caf50' }}></i>
              <span>SOC 2 Compliant</span>
            </div>
          </div>
          <div className="flex space-x-6">
            <a href="#" className="text-sm transition-colors" style={{ color: '#a0b0c0' }}>Privacy Policy</a>
            <a href="#" className="text-sm transition-colors" style={{ color: '#a0b0c0' }}>Terms of Service</a>
            <a href="#" className="text-sm transition-colors" style={{ color: '#a0b0c0' }}>Cookie Policy</a>
          </div>
        </div>
      </div>
    </footer>
  );
}
