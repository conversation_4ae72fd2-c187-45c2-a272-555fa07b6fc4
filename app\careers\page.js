import Header from '../components/Header';

export default function CareersPage() {
  return (
    <div>
      <Header currentPage="careers" />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800 overflow-hidden">
        <div className="absolute inset-0 warehouse-pattern opacity-10"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-[#E6B24B]/10 border border-[#E6B24B]/30 rounded-full mb-6">
              <i className="fas fa-users text-[#E6B24B] mr-2"></i>
              <span className="text-[#E6B24B] font-semibold text-sm uppercase tracking-wide">Join Our Team</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight mb-6">
              Build the Future of
              <span className="block text-[#E6B24B]">Supply Chain Technology</span>
            </h1>
            
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Join a team of passionate professionals who are revolutionizing how enterprises manage their supply chains through flexible warehousing infrastructure.
            </p>
          </div>
        </div>
      </section>

      {/* Why Work at Flexe */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Why Work at Flexe?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We're building the future of logistics technology while creating an environment where talented people can thrive.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-rocket text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Innovation First</h3>
              <p className="text-gray-300 leading-relaxed">
                Work on cutting-edge technology that's transforming how Fortune 500 companies manage their supply chains.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-chart-line text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Growth Opportunities</h3>
              <p className="text-gray-300 leading-relaxed">
                Advance your career in a fast-growing company with opportunities to learn, lead, and make a real impact.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-heart text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Great Benefits</h3>
              <p className="text-gray-300 leading-relaxed">
                Comprehensive health coverage, competitive compensation, flexible work arrangements, and more.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-globe text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Remote Friendly</h3>
              <p className="text-gray-300 leading-relaxed">
                Work from anywhere with our flexible remote work policies and collaborative digital-first culture.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-handshake text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Collaborative Culture</h3>
              <p className="text-gray-300 leading-relaxed">
                Join a team that values collaboration, diversity, and creating an inclusive environment for everyone.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-graduation-cap text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Learning & Development</h3>
              <p className="text-gray-300 leading-relaxed">
                Continuous learning opportunities, conference attendance, and professional development support.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Open Positions
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Explore current opportunities to join our growing team
            </p>
          </div>

          <div className="space-y-6">
            {/* Job Listing */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="mb-4 md:mb-0">
                  <h3 className="text-xl font-bold text-white mb-2">Senior Software Engineer</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-300">
                    <span className="flex items-center"><i className="fas fa-map-marker-alt text-[#E6B24B] mr-2"></i>Seattle, WA / Remote</span>
                    <span className="flex items-center"><i className="fas fa-briefcase text-[#E6B24B] mr-2"></i>Full-time</span>
                    <span className="flex items-center"><i className="fas fa-layer-group text-[#E6B24B] mr-2"></i>Engineering</span>
                  </div>
                </div>
                <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-6 py-3 rounded-xl hover:scale-105 transition-transform duration-300">
                  Apply Now
                </button>
              </div>
            </div>

            {/* Job Listing */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="mb-4 md:mb-0">
                  <h3 className="text-xl font-bold text-white mb-2">Product Manager</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-300">
                    <span className="flex items-center"><i className="fas fa-map-marker-alt text-[#E6B24B] mr-2"></i>Seattle, WA / Remote</span>
                    <span className="flex items-center"><i className="fas fa-briefcase text-[#E6B24B] mr-2"></i>Full-time</span>
                    <span className="flex items-center"><i className="fas fa-layer-group text-[#E6B24B] mr-2"></i>Product</span>
                  </div>
                </div>
                <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-6 py-3 rounded-xl hover:scale-105 transition-transform duration-300">
                  Apply Now
                </button>
              </div>
            </div>

            {/* Job Listing */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="mb-4 md:mb-0">
                  <h3 className="text-xl font-bold text-white mb-2">Logistics Analyst</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-300">
                    <span className="flex items-center"><i className="fas fa-map-marker-alt text-[#E6B24B] mr-2"></i>Seattle, WA</span>
                    <span className="flex items-center"><i className="fas fa-briefcase text-[#E6B24B] mr-2"></i>Full-time</span>
                    <span className="flex items-center"><i className="fas fa-layer-group text-[#E6B24B] mr-2"></i>Operations</span>
                  </div>
                </div>
                <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-6 py-3 rounded-xl hover:scale-105 transition-transform duration-300">
                  Apply Now
                </button>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-300 mb-6">Don't see a position that fits? We're always looking for talented people.</p>
            <a href="/contact" className="border border-[#E6B24B]/30 text-[#E6B24B] hover:bg-[#E6B24B] hover:text-black font-semibold px-8 py-4 rounded-xl transition-all duration-300">
              Send Us Your Resume
            </a>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Ready to Join Our Team?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Be part of the team that's transforming supply chain technology and helping enterprises achieve their logistics goals.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
              Get In Touch
            </a>
            <a href="/about" className="border border-[#E6B24B]/30 text-[#E6B24B] hover:bg-[#E6B24B] hover:text-black font-semibold px-8 py-4 rounded-xl transition-all duration-300">
              Learn About Flexe
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gradient-to-t from-black via-gray-900 to-black border-t border-[#E6B24B]/20 text-white">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">
            {/* Footer Brand Section */}
            <div className="lg:col-span-2">
              <a href="/" className="flex items-center space-x-2 sm:space-x-3 group mb-6 w-fit">
                <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
                </div>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h2>
                  <p className="text-xs text-gray-300 font-medium">Professional Warehousing</p>
                </div>
              </a>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Transform your supply chain with flexible warehousing infrastructure that scales with your business needs and drives sustainable growth.
              </p>
            </div>

            {/* Company */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Company</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/about">About Us</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/careers">Careers</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact</a></li>
              </ul>
            </div>

            {/* Solutions */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Solutions</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Warehousing</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Fulfillment</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Distribution</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Support</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/resources">Resources</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Help Center</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact Support</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-[#E6B24B]/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 Flexe. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm">
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Privacy Policy</a>
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Terms of Service</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
