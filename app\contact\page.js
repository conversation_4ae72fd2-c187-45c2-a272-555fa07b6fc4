'use client';

import { useState } from 'react';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    phone: '',
    message: '',
    interest: 'warehousing'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitted(true);
    }, 2000);
  };

  if (submitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 flex items-center justify-center px-4">
        <div className="max-w-md w-full bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
          <div className="w-16 h-16 bg-[#E6B24B] rounded-full flex items-center justify-center mx-auto mb-6">
            <i className="fas fa-check text-black text-2xl"></i>
          </div>
          <h2 className="text-2xl font-bold text-white mb-4">Thank You!</h2>
          <p className="text-gray-300 mb-6">
            Your message has been sent successfully. One of our supply chain experts will be in touch with you shortly.
          </p>
          <button 
            onClick={() => {
              setSubmitted(false);
              setFormData({
                firstName: '',
                lastName: '',
                email: '',
                company: '',
                phone: '',
                message: '',
                interest: 'warehousing'
              });
            }}
            className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-6 py-3 rounded-xl hover:scale-105 transition-transform duration-300"
          >
            Send Another Message
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="text-white bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* Top banner */}
      <div className="gradient-bg text-black text-xs px-2 sm:px-4 py-2 flex flex-col sm:flex-row justify-between items-center relative overflow-hidden gap-2 sm:gap-0">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse-slow"></div>
        <p className="text-center sm:text-left truncate max-w-full font-medium relative z-10 text-xs sm:text-sm">
          <i className="fas fa-star mr-1 sm:mr-2"></i>
          <span className="hidden sm:inline">Managing Supply Chain Challenges: An Online Dialogue on How to Compete</span>
          <span className="sm:hidden">Supply Chain Solutions Guide</span>
        </p>
        <a className="font-bold text-xs sm:text-sm hover:underline transition-all duration-300 relative z-10 hover:scale-105 whitespace-nowrap" href="#">
          Download Guide
          <i className="fas fa-download ml-1 sm:ml-2"></i>
        </a>
      </div>

      {/* Header */}
      <header className="glass sticky top-0 z-50 border-b border-[#E6B24B]/20 relative overflow-hidden">
        <div className="absolute inset-0 warehouse-pattern opacity-20"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#E6B24B] to-transparent"></div>

        <div className="relative max-w-[90rem] mx-auto px-3 sm:px-6 lg:px-8 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            {/* Logo Section */}
            <div className="flex items-center space-x-2 sm:space-x-3 group">
              <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h1>
                <p className="text-xs text-gray-300 font-medium hidden sm:block">Professional Warehousing</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden lg:flex space-x-6 xl:space-x-8 text-sm font-medium text-white">
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/solutions">
                <span className="hidden xl:inline">Warehousing Solutions</span>
                <span className="xl:hidden">Solutions</span>
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/how-it-works">
                How it Works
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/about">
                Company
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/resources">
                Resources
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
            </nav>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 sm:space-x-4">
              <button className="btn-primary text-black font-semibold px-4 sm:px-6 py-2 rounded-full text-xs sm:text-sm hover-glow">
                Contact Us
              </button>
              <button className="border border-[#E6B24B]/30 text-[#E6B24B] hover:bg-[#E6B24B] hover:text-black font-semibold px-4 sm:px-6 py-2 rounded-full text-xs sm:text-sm transition-all duration-300 hidden sm:block">
                Sign In
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img alt="Modern office building and contact center" className="w-full h-full object-cover opacity-20" src="https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"/>
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-800"></div>
        </div>

        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-[#E6B24B]/10 border border-[#E6B24B]/30 rounded-full mb-8">
            <i className="fas fa-envelope text-[#E6B24B] mr-2"></i>
            <span className="text-[#E6B24B] font-semibold text-sm uppercase tracking-wide">Contact Us</span>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold text-white leading-tight mb-8">
            Ready to future-proof your 
            <span className="block text-[#E6B24B]">logistics network?</span>
          </h1>
          
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Fill out the form and one of our sales representatives will be in touch with you shortly.
          </p>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            
            {/* Contact Information */}
            <div>
              <h2 className="text-3xl font-bold text-white mb-8">Get in Touch</h2>
              
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-[#E6B24B]/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-map-marker-alt text-[#E6B24B]"></i>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Office Address</h3>
                    <p className="text-gray-300">
                      4786 1st Ave S, Suite #106<br/>
                      Seattle, WA 98134<br/>
                      United States
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-[#E6B24B]/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-phone text-[#E6B24B]"></i>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Phone</h3>
                    <p className="text-gray-300">
                      <a href="tel:************" className="hover:text-[#E6B24B] transition-colors duration-300">
                        ************
                      </a>
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-[#E6B24B]/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-envelope text-[#E6B24B]"></i>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Email</h3>
                    <p className="text-gray-300">
                      <a href="mailto:<EMAIL>" className="hover:text-[#E6B24B] transition-colors duration-300">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-[#E6B24B]/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-clock text-[#E6B24B]"></i>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Business Hours</h3>
                    <p className="text-gray-300">
                      Monday - Friday: 8:00 AM - 6:00 PM PST<br/>
                      Saturday - Sunday: Closed
                    </p>
                  </div>
                </div>
              </div>

              {/* Social Links */}
              <div className="mt-12">
                <h3 className="text-lg font-semibold text-white mb-4">Follow Us</h3>
                <div className="flex space-x-4">
                  <a href="#" className="w-10 h-10 bg-gray-800/50 border border-[#E6B24B]/30 rounded-full flex items-center justify-center hover:bg-[#E6B24B] hover:text-black transition-all duration-300">
                    <i className="fab fa-linkedin-in"></i>
                  </a>
                  <a href="#" className="w-10 h-10 bg-gray-800/50 border border-[#E6B24B]/30 rounded-full flex items-center justify-center hover:bg-[#E6B24B] hover:text-black transition-all duration-300">
                    <i className="fab fa-twitter"></i>
                  </a>
                  <a href="#" className="w-10 h-10 bg-gray-800/50 border border-[#E6B24B]/30 rounded-full flex items-center justify-center hover:bg-[#E6B24B] hover:text-black transition-all duration-300">
                    <i className="fas fa-envelope"></i>
                  </a>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6">Send us a Message</h3>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-300 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-gray-800/50 border border-[#E6B24B]/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#E6B24B]/60 transition-colors duration-300"
                      placeholder="Enter your first name"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-300 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-gray-800/50 border border-[#E6B24B]/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#E6B24B]/60 transition-colors duration-300"
                      placeholder="Enter your last name"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-gray-800/50 border border-[#E6B24B]/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#E6B24B]/60 transition-colors duration-300"
                    placeholder="Enter your email address"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-gray-300 mb-2">
                      Company *
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-gray-800/50 border border-[#E6B24B]/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#E6B24B]/60 transition-colors duration-300"
                      placeholder="Enter your company name"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-gray-800/50 border border-[#E6B24B]/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#E6B24B]/60 transition-colors duration-300"
                      placeholder="Enter your phone number"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="interest" className="block text-sm font-medium text-gray-300 mb-2">
                    I'm interested in *
                  </label>
                  <select
                    id="interest"
                    name="interest"
                    value={formData.interest}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-gray-800/50 border border-[#E6B24B]/20 rounded-lg text-white focus:outline-none focus:border-[#E6B24B]/60 transition-colors duration-300"
                  >
                    <option value="warehousing">Warehousing Solutions</option>
                    <option value="fulfillment">Fulfillment Services</option>
                    <option value="distribution">Distribution Network</option>
                    <option value="capacity">Additional Capacity</option>
                    <option value="partnership">Partnership Opportunities</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={5}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-[#E6B24B]/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#E6B24B]/60 transition-colors duration-300 resize-none"
                    placeholder="Tell us about your supply chain needs and how we can help..."
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold py-4 px-6 rounded-xl hover:scale-105 transition-transform duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending Message...
                    </div>
                  ) : (
                    'Send Message'
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gradient-to-t from-black via-gray-900 to-black border-t border-[#E6B24B]/20 text-white">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">
            {/* Footer Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 sm:space-x-3 group mb-6">
                <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
                </div>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h2>
                  <p className="text-xs text-gray-300 font-medium">Professional Warehousing</p>
                </div>
              </div>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Transform your supply chain with flexible warehousing infrastructure that scales with your business needs and drives sustainable growth.
              </p>
            </div>

            {/* Company */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Company</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/about">About Us</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/careers">Careers</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact</a></li>
              </ul>
            </div>

            {/* Solutions */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Solutions</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Warehousing</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Fulfillment</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Distribution</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Support</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/resources">Resources</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Help Center</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact Support</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-[#E6B24B]/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 Flexe. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm">
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Privacy Policy</a>
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Terms of Service</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
