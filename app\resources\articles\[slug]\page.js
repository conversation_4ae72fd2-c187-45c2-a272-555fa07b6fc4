'use client';

import { useState, useEffect } from 'react';
import { notFound } from 'next/navigation';
import { articlesData, resourcesData } from '../../../data/articles';

// Article data imported from shared data file

// Function to get related articles (excluding current article)
function getRelatedArticles(currentSlug, limit = 3) {
  return resourcesData
    .filter(resource => resource.slug !== currentSlug)
    .slice(0, limit);
}

export default function ArticlePage({ params }) {
  const [article, setArticle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [relatedArticles, setRelatedArticles] = useState([]);

  useEffect(() => {
    async function loadArticle() {
      const resolvedParams = await params;
      const articleData = articlesData[resolvedParams.slug];
      if (articleData) {
        setArticle(articleData);
        setRelatedArticles(getRelatedArticles(resolvedParams.slug));
      }
      setLoading(false);
    }
    loadArticle();
  }, [params]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (!article) {
    notFound();
  }

  return (
    <div className="text-white bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* Top banner */}
      <div className="gradient-bg text-black text-xs px-2 sm:px-4 py-2 flex flex-col sm:flex-row justify-between items-center relative overflow-hidden gap-2 sm:gap-0">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse-slow"></div>
        <p className="text-center sm:text-left truncate max-w-full font-medium relative z-10 text-xs sm:text-sm">
          <i className="fas fa-star mr-1 sm:mr-2"></i>
          <span className="hidden sm:inline">Managing Supply Chain Challenges: An Online Dialogue on How to Compete</span>
          <span className="sm:hidden">Supply Chain Solutions Guide</span>
        </p>
        <a className="font-bold text-xs sm:text-sm hover:underline transition-all duration-300 relative z-10 hover:scale-105 whitespace-nowrap" href="#">
          Download Guide
          <i className="fas fa-download ml-1 sm:ml-2"></i>
        </a>
      </div>

      {/* Header */}
      <header className="glass sticky top-0 z-50 border-b border-[#E6B24B]/20 relative overflow-hidden">
        <div className="absolute inset-0 warehouse-pattern opacity-20"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#E6B24B] to-transparent"></div>

        <div className="relative max-w-[90rem] mx-auto px-3 sm:px-6 lg:px-8 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            {/* Logo Section */}
            <div className="flex items-center space-x-2 sm:space-x-3 group">
              <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h1>
                <p className="text-xs text-gray-300 font-medium hidden sm:block">Professional Warehousing</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden lg:flex space-x-6 xl:space-x-8 text-sm font-medium text-white">
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/solutions">
                <span className="hidden xl:inline">Warehousing Solutions</span>
                <span className="xl:hidden">Solutions</span>
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/how-it-works">
                How it Works
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/about">
                Company
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a className="text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/resources">
                Resources
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-[#E6B24B]"></span>
              </a>
            </nav>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 sm:space-x-4">
              <button className="btn-primary text-black font-semibold px-4 sm:px-6 py-2 rounded-full text-xs sm:text-sm hover-glow hidden sm:block">
                Contact Us
              </button>
              <button className="border border-[#E6B24B]/30 text-[#E6B24B] hover:bg-[#E6B24B] hover:text-black font-semibold px-4 sm:px-6 py-2 rounded-full text-xs sm:text-sm transition-all duration-300 hidden sm:block">
                Sign In
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <nav className="flex items-center space-x-2 text-sm text-gray-400">
          <a href="/resources" className="hover:text-[#E6B24B] transition-colors duration-300">Resources</a>
          <i className="fas fa-chevron-right text-xs"></i>
          <a href="/resources" className="hover:text-[#E6B24B] transition-colors duration-300">Articles</a>
          <i className="fas fa-chevron-right text-xs"></i>
          <span className="text-white">{article.title}</span>
        </nav>
      </div>

      {/* Article Header */}
      <section className="relative py-12 overflow-hidden">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <div className="inline-flex items-center px-4 py-2 bg-[#E6B24B]/10 border border-[#E6B24B]/30 rounded-full mb-6">
              <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase mr-3">
                {article.type}
              </span>
              <span className="text-[#E6B24B] font-semibold text-sm uppercase tracking-wide">{article.category}</span>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-white leading-tight mb-6">
              {article.title}
            </h1>

            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
              {article.content.summary}
            </p>

            <div className="flex items-center justify-center space-x-6 text-gray-400 text-sm">
              <div className="flex items-center">
                <i className="fas fa-user mr-2"></i>
                <span>{article.author}</span>
              </div>
              <div className="flex items-center">
                <i className="fas fa-calendar mr-2"></i>
                <span>{article.date}</span>
              </div>
              <div className="flex items-center">
                <i className="fas fa-clock mr-2"></i>
                <span>{article.readTime}</span>
              </div>
            </div>
          </div>

          {/* Featured Image */}
          <div className="relative rounded-2xl overflow-hidden mb-12">
            <img
              alt={article.title}
              className="w-full h-64 md:h-96 object-cover"
              src={article.image}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-12 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Key Takeaways */}
          <div className="bg-gradient-to-br from-[#E6B24B]/10 to-[#E6B24B]/5 border border-[#E6B24B]/20 rounded-2xl p-8 mb-12">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <i className="fas fa-lightbulb text-[#E6B24B] mr-3"></i>
              Key Takeaways
            </h2>
            <ul className="space-y-3">
              {article.content.keyTakeaways.map((takeaway, index) => (
                <li key={index} className="flex items-start text-gray-300">
                  <i className="fas fa-check-circle text-[#E6B24B] mr-3 mt-1 flex-shrink-0"></i>
                  <span>{takeaway}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Article Sections */}
          <div className="prose prose-lg prose-invert max-w-none">
            {article.content.sections.map((section, index) => (
              <div key={index} className="mb-12">
                <h2 className="text-3xl font-bold text-white mb-6">{section.heading}</h2>
                <p className="text-gray-300 leading-relaxed text-lg">{section.content}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Ready to Transform Your Supply Chain?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Discover how Flexe's flexible warehousing infrastructure can help your business achieve similar results.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
              Contact Our Experts
            </a>
            <a href="/resources" className="border border-[#E6B24B]/30 text-[#E6B24B] hover:bg-[#E6B24B] hover:text-black font-semibold px-8 py-4 rounded-xl transition-all duration-300">
              Explore More Resources
            </a>
          </div>
        </div>
      </section>

      {/* Share Section */}
      <section className="py-12 border-t border-[#E6B24B]/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row items-center justify-between">
            <div className="mb-4 sm:mb-0">
              <h3 className="text-xl font-bold text-white mb-2">Share this article</h3>
              <p className="text-gray-400">Help others discover valuable supply chain insights</p>
            </div>

            <div className="flex items-center space-x-4">
              <a href="#" className="w-12 h-12 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center transition-colors duration-300">
                <i className="fab fa-linkedin-in text-white"></i>
              </a>
              <a href="#" className="w-12 h-12 bg-blue-400 hover:bg-blue-500 rounded-full flex items-center justify-center transition-colors duration-300">
                <i className="fab fa-twitter text-white"></i>
              </a>
              <a href="#" className="w-12 h-12 bg-blue-800 hover:bg-blue-900 rounded-full flex items-center justify-center transition-colors duration-300">
                <i className="fab fa-facebook-f text-white"></i>
              </a>
              <a href="#" className="w-12 h-12 bg-gray-600 hover:bg-gray-700 rounded-full flex items-center justify-center transition-colors duration-300">
                <i className="fas fa-envelope text-white"></i>
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      <section className="py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Related Articles</h2>
            <p className="text-xl text-gray-300">
              Continue exploring our supply chain insights and best practices
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {relatedArticles.map((relatedArticle) => (
              <div key={relatedArticle.id} className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl overflow-hidden hover:border-[#E6B24B]/40 transition-all duration-300 group">
                <div className="relative overflow-hidden">
                  <img
                    alt={relatedArticle.title}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    src={relatedArticle.image}
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                      {relatedArticle.type}
                    </span>
                  </div>
                  {relatedArticle.featured && (
                    <div className="absolute top-4 right-4">
                      <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                        Featured
                      </span>
                    </div>
                  )}
                </div>

                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-3 group-hover:text-[#E6B24B] transition-colors duration-300">
                    {relatedArticle.title}
                  </h3>
                  <p className="text-gray-300 mb-4 leading-relaxed text-sm">
                    {relatedArticle.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">{relatedArticle.readTime}</span>
                    <a href={`/resources/articles/${relatedArticle.slug}`} className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                      Read More <i className="fas fa-arrow-right ml-1"></i>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gradient-to-t from-black via-gray-900 to-black border-t border-[#E6B24B]/20 text-white">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">
            {/* Footer Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 sm:space-x-3 group mb-6">
                <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
                </div>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h2>
                  <p className="text-xs text-gray-300 font-medium">Professional Warehousing</p>
                </div>
              </div>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Transform your supply chain with flexible warehousing infrastructure that scales with your business needs and drives sustainable growth.
              </p>
            </div>

            {/* Company */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Company</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/about">About Us</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/careers">Careers</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact</a></li>
              </ul>
            </div>

            {/* Solutions */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Solutions</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Warehousing</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Fulfillment</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Distribution</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Support</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/resources">Resources</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Help Center</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact Support</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-[#E6B24B]/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 Flexe. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm">
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Privacy Policy</a>
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Terms of Service</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
