'use client';

import { useState } from 'react';
import Image from "next/image";
import { resourcesData } from '../data/articles';
import Header from '../components/Header';

export default function ResourcesPage() {
  const [activeFilter, setActiveFilter] = useState('all');

  const resources = resourcesData;

  const filteredResources = activeFilter === 'all'
    ? resources
    : resources.filter(resource => resource.category === activeFilter);

  const featuredResources = resources.filter(resource => resource.featured);

  return (
    <div>
      <Header currentPage="resources" />

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img alt="Modern library and knowledge resources" className="w-full h-full object-cover opacity-20" src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"/>
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-800"></div>
        </div>

        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-[#E6B24B]/10 border border-[#E6B24B]/30 rounded-full mb-8">
            <i className="fas fa-book-open text-[#E6B24B] mr-2"></i>
            <span className="text-[#E6B24B] font-semibold text-sm uppercase tracking-wide">Resources</span>
          </div>

          <h1 className="text-5xl md:text-6xl font-bold text-white leading-tight mb-8">
            Supply Chain
            <span className="block text-[#E6B24B]">Knowledge Hub</span>
          </h1>

          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Explore our comprehensive collection of insights, research, and best practices to optimize your supply chain operations.
          </p>
        </div>
      </section>

      {/* Featured Resources */}
      <section className="py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Featured Insights</h2>
            <p className="text-xl text-gray-300">
              Our most popular and impactful resources for supply chain professionals
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {featuredResources.map((resource) => (
              <div key={resource.id} className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl overflow-hidden hover:border-[#E6B24B]/40 transition-all duration-300 group">
                <div className="relative overflow-hidden">
                  <img
                    alt={resource.title}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    src={resource.image}
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                      {resource.type}
                    </span>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-3 group-hover:text-[#E6B24B] transition-colors duration-300">
                    {resource.title}
                  </h3>
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    {resource.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">{resource.readTime}</span>
                    <a href={`/resources/articles/${resource.slug}`} className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                      Read More <i className="fas fa-arrow-right ml-1"></i>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* All Resources Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">All Resources</h2>

            {/* Filter Buttons */}
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <button
                onClick={() => setActiveFilter('all')}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  activeFilter === 'all'
                    ? 'bg-[#E6B24B] text-black'
                    : 'bg-gray-800/50 text-gray-300 hover:text-white border border-[#E6B24B]/20 hover:border-[#E6B24B]/40'
                }`}
              >
                All Resources
              </button>
              <button
                onClick={() => setActiveFilter('case-study')}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  activeFilter === 'case-study'
                    ? 'bg-[#E6B24B] text-black'
                    : 'bg-gray-800/50 text-gray-300 hover:text-white border border-[#E6B24B]/20 hover:border-[#E6B24B]/40'
                }`}
              >
                Case Studies
              </button>
              <button
                onClick={() => setActiveFilter('insights')}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  activeFilter === 'insights'
                    ? 'bg-[#E6B24B] text-black'
                    : 'bg-gray-800/50 text-gray-300 hover:text-white border border-[#E6B24B]/20 hover:border-[#E6B24B]/40'
                }`}
              >
                Articles
              </button>
              <button
                onClick={() => setActiveFilter('research')}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  activeFilter === 'research'
                    ? 'bg-[#E6B24B] text-black'
                    : 'bg-gray-800/50 text-gray-300 hover:text-white border border-[#E6B24B]/20 hover:border-[#E6B24B]/40'
                }`}
              >
                White Papers
              </button>
              <button
                onClick={() => setActiveFilter('education')}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  activeFilter === 'education'
                    ? 'bg-[#E6B24B] text-black'
                    : 'bg-gray-800/50 text-gray-300 hover:text-white border border-[#E6B24B]/20 hover:border-[#E6B24B]/40'
                }`}
              >
                Webinars
              </button>
            </div>
          </div>

          {/* Resources Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredResources.map((resource) => (
              <div key={resource.id} className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl overflow-hidden hover:border-[#E6B24B]/40 transition-all duration-300 group">
                <div className="relative overflow-hidden">
                  <img
                    alt={resource.title}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    src={resource.image}
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                      {resource.type}
                    </span>
                  </div>
                  {resource.featured && (
                    <div className="absolute top-4 right-4">
                      <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                        Featured
                      </span>
                    </div>
                  )}
                </div>

                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-3 group-hover:text-[#E6B24B] transition-colors duration-300">
                    {resource.title}
                  </h3>
                  <p className="text-gray-300 mb-4 leading-relaxed text-sm">
                    {resource.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">{resource.readTime}</span>
                    <a href={`/resources/articles/${resource.slug}`} className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                      Read More <i className="fas fa-arrow-right ml-1"></i>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-6">
              Stay Updated with Supply Chain Insights
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Subscribe to our newsletter for the latest trends, insights, and best practices in supply chain management.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-6 py-4 rounded-xl bg-gray-800/50 border border-[#E6B24B]/20 text-white placeholder-gray-400 focus:outline-none focus:border-[#E6B24B]/60"
              />
              <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300 whitespace-nowrap">
                Subscribe
              </button>
            </div>

            <p className="text-gray-400 text-sm mt-4">
              No spam, unsubscribe at any time. We respect your privacy.
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gradient-to-t from-black via-gray-900 to-black border-t border-[#E6B24B]/20 text-white">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">
            {/* Footer Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 sm:space-x-3 group mb-6">
                <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
                </div>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h2>
                  <p className="text-xs text-gray-300 font-medium">Professional Warehousing</p>
                </div>
              </div>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Transform your supply chain with flexible warehousing infrastructure that scales with your business needs and drives sustainable growth.
              </p>
            </div>

            {/* Company */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Company</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/about">About Us</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/careers">Careers</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact</a></li>
              </ul>
            </div>

            {/* Solutions */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Solutions</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Warehousing</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Fulfillment</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Distribution</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Support</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/resources">Resources</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Help Center</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact Support</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-[#E6B24B]/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 Flexe. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm">
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Privacy Policy</a>
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Terms of Service</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
