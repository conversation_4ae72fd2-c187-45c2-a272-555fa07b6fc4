import Header from './components/Header';

export default function Home() {
  return (
    <div className="font-sans bg-white">
      <Header currentPage="home" />

      {/* HERO SECTION - CLEAN & PROFESSIONAL */}
      <section className="relative min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 overflow-hidden">

        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-6 lg:px-8 pt-20 pb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-screen">

            {/* Left Content */}
            <div className="space-y-8">

              {/* Badge */}
              <div className="inline-flex items-center px-4 py-2 bg-blue-50 border border-blue-200 rounded-full">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3 animate-pulse"></div>
                <span className="text-blue-700 font-semibold text-sm">Enterprise Warehousing Solutions</span>
              </div>

              {/* Main Headline */}
              <div className="space-y-6">
                <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight">
                  Transform Your
                  <span className="block text-blue-600">Supply Chain</span>
                </h1>

                <p className="text-xl text-gray-600 leading-relaxed max-w-2xl">
                  Leverage our cutting-edge, flexible warehousing infrastructure to optimize efficiency and drive sustainable growth across your entire operation.
                </p>
              </div>

              {/* Key Stats */}
              <div className="grid grid-cols-3 gap-8 py-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-2">1000+</div>
                  <div className="text-sm text-gray-600">Warehouse Locations</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-2">99.9%</div>
                  <div className="text-sm text-gray-600">Uptime Guarantee</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-2">500+</div>
                  <div className="text-sm text-gray-600">Enterprise Clients</div>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl">
                  Get Started Today
                </button>
                <button className="border border-gray-300 hover:border-gray-400 text-gray-700 font-semibold px-8 py-4 rounded-lg transition-colors duration-200">
                  Watch Demo
                </button>
              </div>

            </div>

            {/* Right Side - Hero Image */}
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                  alt="Modern warehouse facility"
                  className="w-full h-96 lg:h-[500px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>

                {/* Floating Stats Card */}
                <div className="absolute bottom-6 left-6 right-6">
                  <div className="bg-white/95 backdrop-blur-sm rounded-xl p-6 shadow-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-2xl font-bold text-gray-900">$2.5M</div>
                        <div className="text-sm text-gray-600">Average Cost Savings</div>
                      </div>
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <i className="fas fa-arrow-up text-green-600 text-lg"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>

      {/* SOLUTIONS SECTION - CLEAN & MODERN */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">

          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-50 border border-blue-200 rounded-full mb-6">
              <i className="fas fa-cogs text-blue-600 mr-2"></i>
              <span className="text-blue-700 font-semibold text-sm">Our Solutions</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Comprehensive Warehousing Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover how our flexible warehousing infrastructure transforms your business operations and drives growth.
            </p>
          </div>

          {/* Solutions Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">

            {/* Solution 1 */}
            <div className="bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-chart-line text-blue-600 text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Finance Solutions</h3>
              <p className="text-gray-600 mb-6">
                Mitigate financial risks and optimize cash flow with flexible warehousing infrastructure.
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start">
                  <i className="fas fa-check text-blue-600 mt-1 mr-3"></i>
                  <span className="text-gray-600">Manage excess inventory efficiently</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-blue-600 mt-1 mr-3"></i>
                  <span className="text-gray-600">Secure space during slowdowns</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-blue-600 mt-1 mr-3"></i>
                  <span className="text-gray-600">Mitigate cash flow disruptions</span>
                </li>
              </ul>
              <button className="text-blue-600 font-semibold hover:text-blue-700 transition-colors">
                Learn More →
              </button>
            </div>

            {/* Solution 2 */}
            <div className="bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-truck text-green-600 text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Logistics Solutions</h3>
              <p className="text-gray-600 mb-6">
                Optimize your supply chain with our nationwide network of flexible warehouse facilities.
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start">
                  <i className="fas fa-check text-green-600 mt-1 mr-3"></i>
                  <span className="text-gray-600">Access 1000+ warehouse locations</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-green-600 mt-1 mr-3"></i>
                  <span className="text-gray-600">Flexible capacity scaling</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-green-600 mt-1 mr-3"></i>
                  <span className="text-gray-600">Real-time inventory tracking</span>
                </li>
              </ul>
              <button className="text-green-600 font-semibold hover:text-green-700 transition-colors">
                Learn More →
              </button>
            </div>

            {/* Solution 3 */}
            <div className="bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-cogs text-purple-600 text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Operations Solutions</h3>
              <p className="text-gray-600 mb-6">
                Streamline operations with advanced technology and dedicated support teams.
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start">
                  <i className="fas fa-check text-purple-600 mt-1 mr-3"></i>
                  <span className="text-gray-600">Advanced WMS integration</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-purple-600 mt-1 mr-3"></i>
                  <span className="text-gray-600">Dedicated account management</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-purple-600 mt-1 mr-3"></i>
                  <span className="text-gray-600">24/7 operational support</span>
                </li>
              </ul>
              <button className="text-purple-600 font-semibold hover:text-purple-700 transition-colors">
                Learn More →
              </button>
            </div>

          </div>
        </div>
      </section>

      {/* DARK SOLUTIONS SECTION */}
      <section className="py-20 bg-gray-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-[#E6B24B]/5 via-transparent to-[#E6B24B]/5"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-[#E6B24B]/10 border border-[#E6B24B]/30 rounded-full mb-6">
              <i className="fas fa-cogs text-[#E6B24B] mr-2"></i>
              <span className="text-[#E6B24B] font-semibold text-sm uppercase tracking-wide">Our Solutions</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Comprehensive <span className="text-[#E6B24B]">Warehousing Solutions</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Discover how our flexible warehousing infrastructure transforms your business operations and drives growth
            </p>
          </div>

          {/* Solution Categories */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {/* Finance Solutions */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300 group">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-chart-line text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Finance Solutions</h3>
              <p className="text-gray-300 mb-6">
                Mitigate financial risks and optimize cash flow with flexible warehousing infrastructure.
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#E6B24B] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm">Manage excess inventory efficiently</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#E6B24B] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm">Secure space during slowdowns</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#E6B24B] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm">Mitigate cash flow disruptions</span>
                </li>
              </ul>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300 group-hover:translate-x-2 transform">
                Learn More <i className="fas fa-arrow-right ml-2"></i>
              </button>
            </div>

            {/* Logistics Solutions */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300 group">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-truck text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Logistics Solutions</h3>
              <p className="text-gray-300 mb-6">
                Optimize your supply chain with our nationwide network of flexible warehouse facilities.
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#E6B24B] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm">Access 1000+ warehouse locations</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#E6B24B] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm">Flexible capacity scaling</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#E6B24B] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm">Real-time inventory tracking</span>
                </li>
              </ul>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300 group-hover:translate-x-2 transform">
                Learn More <i className="fas fa-arrow-right ml-2"></i>
              </button>
            </div>

            {/* Operations Solutions */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 hover:border-[#E6B24B]/40 transition-all duration-300 group">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-cogs text-black text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Operations Solutions</h3>
              <p className="text-gray-300 mb-6">
                Streamline operations with advanced technology and dedicated support teams.
              </p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#E6B24B] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm">Advanced WMS integration</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#E6B24B] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm">Dedicated account management</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#E6B24B] rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm">24/7 operational support</span>
                </li>
              </ul>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300 group-hover:translate-x-2 transform">
                Learn More <i className="fas fa-arrow-right ml-2"></i>
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* TESTIMONIALS SECTION */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">

          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Trusted by Industry Leaders
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See how companies like yours are transforming their supply chains with Flexe.
            </p>
          </div>

          {/* Testimonials Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">

            {/* Testimonial 1 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <i className="fas fa-quote-left text-blue-600"></i>
                </div>
                <div>
                  <div className="font-bold text-gray-900">Sarah Johnson</div>
                  <div className="text-sm text-gray-600">VP of Operations, TechCorp</div>
                </div>
              </div>
              <p className="text-gray-600 leading-relaxed">
                "Flexe helped us reduce warehousing costs by 40% while improving our delivery times. Their flexible network is exactly what we needed."
              </p>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                  <i className="fas fa-quote-left text-green-600"></i>
                </div>
                <div>
                  <div className="font-bold text-gray-900">Michael Chen</div>
                  <div className="text-sm text-gray-600">Supply Chain Director, RetailPlus</div>
                </div>
              </div>
              <p className="text-gray-600 leading-relaxed">
                "The scalability and technology integration exceeded our expectations. We can now handle seasonal peaks without long-term commitments."
              </p>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                  <i className="fas fa-quote-left text-purple-600"></i>
                </div>
                <div>
                  <div className="font-bold text-gray-900">Emily Rodriguez</div>
                  <div className="text-sm text-gray-600">Logistics Manager, GlobalTrade</div>
                </div>
              </div>
              <p className="text-gray-600 leading-relaxed">
                "Flexe's real-time visibility and control transformed how we manage our inventory across multiple locations. Game-changing solution."
              </p>
            </div>

          </div>
        </div>
      </section>

      {/* CTA SECTION */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-4xl mx-auto px-6 lg:px-8 text-center">
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            Ready to Transform Your Supply Chain?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join hundreds of companies already using Flexe to optimize their warehousing operations and reduce costs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-blue-600 font-semibold px-8 py-4 rounded-lg hover:bg-gray-50 transition-colors duration-200 shadow-lg">
              Get Started Today
            </button>
            <button className="border-2 border-white text-white font-semibold px-8 py-4 rounded-lg hover:bg-white hover:text-blue-600 transition-colors duration-200">
              Schedule a Demo
            </button>
          </div>
        </div>
      </section>

      {/* FOOTER */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">

            {/* Company Info */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <i className="fas fa-warehouse text-white text-lg"></i>
                </div>
                <span className="text-2xl font-bold">Flexe</span>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Transform your supply chain with our flexible warehousing solutions. Trusted by Fortune 500 companies worldwide.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors">
                  <i className="fab fa-linkedin text-gray-400"></i>
                </a>
                <a href="#" className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors">
                  <i className="fab fa-twitter text-gray-400"></i>
                </a>
                <a href="#" className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors">
                  <i className="fab fa-facebook text-gray-400"></i>
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="font-bold text-lg mb-6">Solutions</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Warehousing</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Distribution</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Fulfillment</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Technology</a></li>
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="font-bold text-lg mb-6">Company</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Support</a></li>
              </ul>
            </div>

          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Flexe. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
