'use client';

import { useState } from 'react';
import Image from "next/image";

export default function SolutionsPage() {
  const [activeTab, setActiveTab] = useState('distribution');

  return (
    <div className="text-white bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* Top banner */}
      <div className="gradient-bg text-black text-xs px-2 sm:px-4 py-2 flex flex-col sm:flex-row justify-between items-center relative overflow-hidden gap-2 sm:gap-0">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse-slow"></div>
        <p className="text-center sm:text-left truncate max-w-full font-medium relative z-10 text-xs sm:text-sm">
          <i className="fas fa-star mr-1 sm:mr-2"></i>
          <span className="hidden sm:inline">Managing Supply Chain Challenges: An Online Dialogue on How to Compete</span>
          <span className="sm:hidden">Supply Chain Solutions Guide</span>
        </p>
        <a className="font-bold text-xs sm:text-sm hover:underline transition-all duration-300 relative z-10 hover:scale-105 whitespace-nowrap" href="#">
          Download Guide
          <i className="fas fa-download ml-1 sm:ml-2"></i>
        </a>
      </div>

      {/* Header */}
      <header className="glass sticky top-0 z-50 border-b border-[#E6B24B]/20 relative overflow-hidden">
        <div className="absolute inset-0 warehouse-pattern opacity-20"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-[#E6B24B] to-transparent"></div>

        <div className="relative max-w-[90rem] mx-auto px-3 sm:px-6 lg:px-8 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            {/* Logo Section */}
            <div className="flex items-center space-x-2 sm:space-x-3 group">
              <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h1>
                <p className="text-xs text-gray-300 font-medium hidden sm:block">Professional Warehousing</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden lg:flex space-x-6 xl:space-x-8 text-sm font-medium text-white">
              <a className="text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/solutions">
                <span className="hidden xl:inline">Warehousing Solutions</span>
                <span className="xl:hidden">Solutions</span>
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-[#E6B24B]"></span>
              </a>
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/how-it-works">
                How it Works
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/about">
                Company
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a className="hover:text-[#E6B24B] transition-all duration-300 hover:scale-105 relative group" href="/resources">
                Resources
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#E6B24B] transition-all duration-300 group-hover:w-full"></span>
              </a>
            </nav>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 sm:space-x-4">
              <button className="btn-primary text-black font-semibold px-4 sm:px-6 py-2 rounded-full text-xs sm:text-sm hover-glow hidden sm:block">
                Contact Us
              </button>
              <button className="border border-[#E6B24B]/30 text-[#E6B24B] hover:bg-[#E6B24B] hover:text-black font-semibold px-4 sm:px-6 py-2 rounded-full text-xs sm:text-sm transition-all duration-300 hidden sm:block">
                Sign In
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img alt="Modern warehouse with geometric patterns" className="w-full h-full object-cover opacity-20" src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"/>
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-800"></div>
        </div>

        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <h1 className="text-5xl md:text-6xl font-bold text-white leading-tight">
                Flexible Warehousing Infrastructure allows enterprises to 
                <span className="block text-[#E6B24B]">evolve, optimize and prepare</span> networks.
              </h1>
              <p className="text-xl text-gray-300 leading-relaxed">
                Partner with Flexe to dynamically add warehouses when, where and for as long as necessary.
              </p>
            </div>
            <div className="relative">
              <img alt="Abstract geometric warehouse design" className="w-full h-96 object-cover rounded-2xl" src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"/>
            </div>
          </div>
        </div>
      </section>

      {/* Why Flexe Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <div className="inline-flex items-center px-4 py-2 bg-[#E6B24B]/10 border border-[#E6B24B]/30 rounded-full mb-8">
                <span className="text-[#E6B24B] font-semibold text-sm uppercase tracking-wide">Why Flexe</span>
              </div>
              <h2 className="text-4xl font-bold text-white mb-8">
                Complement fixed networks with tech-enabled warehouse services.
              </h2>
              
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-[#E6B24B]/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-expand-arrows-alt text-[#E6B24B]"></i>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">Scale with an asset-light and agile approach</h3>
                    <p className="text-gray-300">Complement fixed warehousing infrastructure with Flexible Warehousing Infrastructure. No CapEx, fixed-term agreements or fixed costs.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-[#E6B24B]/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-network-wired text-[#E6B24B]"></i>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">Expand, consolidate and optimize networks</h3>
                    <p className="text-gray-300">Transform supply chains fast with North America's largest flexible warehousing network of over 700+ unique warehouse operators.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-[#E6B24B]/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-puzzle-piece text-[#E6B24B]"></i>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">Solve the most difficult supply chain problems—flexibly</h3>
                    <p className="text-gray-300">Improve delivery promise, reduce transportation costs, respond to supply chain disruptions or changes in demand and prevent production slowdowns—fast. Eliminate leases, start-up costs, and inefficiency.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <img alt="White brick wall texture" className="w-full h-96 object-cover rounded-2xl" src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"/>
            </div>
          </div>
        </div>
      </section>

      {/* Solutions Tabs Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Tab Navigation */}
          <div className="flex justify-center mb-16">
            <div className="flex space-x-8 bg-gray-800/50 rounded-full p-2">
              <button 
                onClick={() => setActiveTab('distribution')}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  activeTab === 'distribution' 
                    ? 'bg-[#E6B24B] text-black' 
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                Distribution
              </button>
              <button 
                onClick={() => setActiveTab('fulfillment')}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  activeTab === 'fulfillment' 
                    ? 'bg-[#E6B24B] text-black' 
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                Fulfillment
              </button>
              <button 
                onClick={() => setActiveTab('capacity')}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                  activeTab === 'capacity' 
                    ? 'bg-[#E6B24B] text-black' 
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                Capacity
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {activeTab === 'distribution' && (
              <>
                <div>
                  <h2 className="text-4xl font-bold text-white mb-6">
                    Get critical products to market. Quickly.
                  </h2>
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Improve store replenishment SLAs</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Enter/Expand into new markets and channels</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Consolidate and optimize networks</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">On/nearshoring</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Inventory build for seasonal products and peak demand</span>
                    </li>
                  </ul>
                  <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
                    Learn More
                  </button>
                </div>
                <div className="space-y-8">
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">124%</div>
                    <div className="text-white font-semibold mb-2">ROI when partnering with Flexe</div>
                    <div className="text-gray-400 text-sm">Forrester Research Total Economic Impact™ (TEI) Study of Flexe Logistics Programs, 2023</div>
                  </div>
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">90%+</div>
                    <div className="text-white font-semibold mb-2">of consumers say they will buy another brand if their preferred choice isn't available</div>
                    <div className="text-gray-400 text-sm">The Wall Street Journal, Brand Loyalty Takes a Hit From Inflation, Shortages, 2022</div>
                  </div>
                </div>
              </>
            )}

            {activeTab === 'fulfillment' && (
              <>
                <div>
                  <h2 className="text-4xl font-bold text-white mb-6">
                    Unlock innovative customer experiences. Capture market share.
                  </h2>
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Faster eComm/Omnichannel fulfillment</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Reduce transportation costs</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Test new sales channels</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Launch new products fast</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Seasonal SKU optimization</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Optimize big and bulky SKUs</span>
                    </li>
                  </ul>
                  <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
                    Learn More
                  </button>
                </div>
                <div className="space-y-8">
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">21%</div>
                    <div className="text-white font-semibold mb-2">of consumers purchase a new product as soon as it comes out</div>
                    <div className="text-gray-400 text-sm">20+ Product Launch statistics you should know 2024, Learn.g2, 2024</div>
                  </div>
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">83%</div>
                    <div className="text-white font-semibold mb-2">of consumers switch retailers for faster delivery</div>
                    <div className="text-gray-400 text-sm">The 2022 Omnichannel Retail Report.", Flexe Institute, Jun 06, 2022</div>
                  </div>
                </div>
              </>
            )}

            {activeTab === 'capacity' && (
              <>
                <div>
                  <h2 className="text-4xl font-bold text-white mb-6">
                    Storage when and where it's needed.
                  </h2>
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Disaster relief</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Forecast misses</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Manage excess inventory</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Opportunistic bulk buys</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#E6B24B] rounded-full"></div>
                      <span className="text-gray-300">Raw materials management</span>
                    </li>
                  </ul>
                  <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-8 py-4 rounded-xl hover:scale-105 transition-transform duration-300">
                    Learn More
                  </button>
                </div>
                <div className="space-y-8">
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">50%+</div>
                    <div className="text-white font-semibold mb-2">reduction in new facility ramp time for Flexe customers</div>
                    <div className="text-gray-400 text-sm">Forrester Research Total Economic Impact™ (TEI) Study of Flexe Logistics Programs, 2023</div>
                  </div>
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center">
                    <div className="text-5xl font-bold text-[#E6B24B] mb-2">$82M</div>
                    <div className="text-white font-semibold mb-2">Average yearly cost of global supply chain disruptions for large companies</div>
                    <div className="text-gray-400 text-sm">Reuters, 2023</div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Discover Flexible Warehousing Infrastructure
          </h2>
          <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-10 py-5 rounded-xl text-lg hover:scale-105 transition-transform duration-300 shadow-lg">
            Contact Us
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gradient-to-t from-black via-gray-900 to-black border-t border-[#E6B24B]/20 text-white">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">
            {/* Footer Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 sm:space-x-3 group mb-6">
                <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
                </div>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h2>
                  <p className="text-xs text-gray-300 font-medium">Professional Warehousing</p>
                </div>
              </div>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Transform your supply chain with flexible warehousing infrastructure that scales with your business needs and drives sustainable growth.
              </p>
            </div>

            {/* Company */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Company</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/about">About Us</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/careers">Careers</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact</a></li>
              </ul>
            </div>

            {/* Solutions */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Solutions</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Warehousing</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Fulfillment</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Distribution</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Support</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/resources">Resources</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Help Center</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact Support</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-[#E6B24B]/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 Flexe. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm">
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Privacy Policy</a>
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Terms of Service</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
