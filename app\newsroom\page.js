import Header from '../components/Header';

export default function NewsroomPage() {
  return (
    <div>
      <Header currentPage="newsroom" />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800 overflow-hidden">
        <div className="absolute inset-0 warehouse-pattern opacity-10"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-[#E6B24B]/10 border border-[#E6B24B]/30 rounded-full mb-6">
              <i className="fas fa-newspaper text-[#E6B24B] mr-2"></i>
              <span className="text-[#E6B24B] font-semibold text-sm uppercase tracking-wide">Newsroom</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight mb-6">
              Latest News &
              <span className="block text-[#E6B24B]">Company Updates</span>
            </h1>
            
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Stay up to date with the latest developments, partnerships, and innovations from Flexe as we continue to transform the supply chain industry.
            </p>
          </div>
        </div>
      </section>

      {/* Featured News */}
      <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Featured News
            </h2>
          </div>

          {/* Featured Article */}
          <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl overflow-hidden hover:border-[#E6B24B]/40 transition-all duration-300 mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
              <div className="relative">
                <img 
                  alt="Flexe announces major partnership"
                  className="w-full h-64 lg:h-full object-cover"
                  src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
              </div>
              <div className="p-8 lg:p-12">
                <div className="flex items-center mb-4">
                  <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase mr-3">
                    Press Release
                  </span>
                  <span className="text-gray-400 text-sm">December 15, 2024</span>
                </div>
                <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4">
                  Flexe Announces Strategic Partnership with Major Retail Chain
                </h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Flexe today announced a strategic partnership that will expand flexible warehousing infrastructure across key markets, enabling faster delivery times and improved customer satisfaction.
                </p>
                <a href="#" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                  Read Full Story <i className="fas fa-arrow-right ml-2"></i>
                </a>
              </div>
            </div>
          </div>

          {/* News Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* News Item */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl overflow-hidden hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="relative">
                <img 
                  alt="Flexe funding announcement"
                  className="w-full h-48 object-cover"
                  src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                    Funding
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="text-gray-400 text-sm mb-3">December 10, 2024</div>
                <h3 className="text-lg font-bold text-white mb-3">
                  Flexe Raises $50M Series C to Accelerate Growth
                </h3>
                <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                  New funding will support expansion of flexible warehousing network and technology platform enhancements.
                </p>
                <a href="#" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300 text-sm">
                  Read More <i className="fas fa-arrow-right ml-1"></i>
                </a>
              </div>
            </div>

            {/* News Item */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl overflow-hidden hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="relative">
                <img 
                  alt="Flexe award recognition"
                  className="w-full h-48 object-cover"
                  src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                    Award
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="text-gray-400 text-sm mb-3">December 5, 2024</div>
                <h3 className="text-lg font-bold text-white mb-3">
                  Flexe Named "Supply Chain Innovation Leader"
                </h3>
                <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                  Industry recognition for pioneering flexible warehousing infrastructure solutions.
                </p>
                <a href="#" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300 text-sm">
                  Read More <i className="fas fa-arrow-right ml-1"></i>
                </a>
              </div>
            </div>

            {/* News Item */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl overflow-hidden hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="relative">
                <img 
                  alt="Flexe product launch"
                  className="w-full h-48 object-cover"
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-[#E6B24B] text-black px-3 py-1 rounded-full text-xs font-semibold uppercase">
                    Product
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="text-gray-400 text-sm mb-3">November 28, 2024</div>
                <h3 className="text-lg font-bold text-white mb-3">
                  New AI-Powered Analytics Platform Launched
                </h3>
                <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                  Advanced data intelligence capabilities help enterprises optimize their supply chain networks.
                </p>
                <a href="#" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300 text-sm">
                  Read More <i className="fas fa-arrow-right ml-1"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Press Kit */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Press Kit & Media Resources
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Download logos, images, and company information for media use
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-image text-black text-2xl"></i>
              </div>
              <h3 className="text-lg font-bold text-white mb-4">Company Logos</h3>
              <p className="text-gray-300 text-sm mb-6">High-resolution logos in various formats</p>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                Download <i className="fas fa-download ml-2"></i>
              </button>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-file-alt text-black text-2xl"></i>
              </div>
              <h3 className="text-lg font-bold text-white mb-4">Fact Sheet</h3>
              <p className="text-gray-300 text-sm mb-6">Key company facts and statistics</p>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                Download <i className="fas fa-download ml-2"></i>
              </button>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-users text-black text-2xl"></i>
              </div>
              <h3 className="text-lg font-bold text-white mb-4">Executive Bios</h3>
              <p className="text-gray-300 text-sm mb-6">Leadership team biographies</p>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                Download <i className="fas fa-download ml-2"></i>
              </button>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 text-center hover:border-[#E6B24B]/40 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-camera text-black text-2xl"></i>
              </div>
              <h3 className="text-lg font-bold text-white mb-4">Product Images</h3>
              <p className="text-gray-300 text-sm mb-6">Screenshots and product visuals</p>
              <button className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
                Download <i className="fas fa-download ml-2"></i>
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Media Contact */}
      <section className="py-20 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Media Inquiries
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            For press inquiries, interviews, or additional information, please contact our media relations team.
          </p>
          
          <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8 max-w-md mx-auto">
            <div className="w-16 h-16 bg-gradient-to-br from-[#E6B24B] to-[#D4A843] rounded-xl flex items-center justify-center mx-auto mb-6">
              <i className="fas fa-envelope text-black text-2xl"></i>
            </div>
            <h3 className="text-xl font-bold text-white mb-4">Press Contact</h3>
            <p className="text-gray-300 mb-2">Media Relations Team</p>
            <a href="mailto:<EMAIL>" className="text-[#E6B24B] font-semibold hover:text-white transition-colors duration-300">
              <EMAIL>
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gradient-to-t from-black via-gray-900 to-black border-t border-[#E6B24B]/20 text-white">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">
            {/* Footer Brand Section */}
            <div className="lg:col-span-2">
              <a href="/" className="flex items-center space-x-2 sm:space-x-3 group mb-6 w-fit">
                <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
                </div>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h2>
                  <p className="text-xs text-gray-300 font-medium">Professional Warehousing</p>
                </div>
              </a>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Transform your supply chain with flexible warehousing infrastructure that scales with your business needs and drives sustainable growth.
              </p>
            </div>

            {/* Company */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Company</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/about">About Us</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/careers">Careers</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact</a></li>
              </ul>
            </div>

            {/* Solutions */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Solutions</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Warehousing</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Fulfillment</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Distribution</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Support</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/resources">Resources</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Help Center</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact Support</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-[#E6B24B]/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 Flexe. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm">
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Privacy Policy</a>
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Terms of Service</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
