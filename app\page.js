'use client';

import { useEffect } from 'react';
import Header from './components/Header';
import Footer from './components/Footer';

export default function Home() {
  // Intersection Observer for scroll-triggered animations
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in-view');
          // Only animate once
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe all elements with scroll-animate class
    const animateElements = document.querySelectorAll('.scroll-animate');
    animateElements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);
  return (
    <>
      <div style={{ backgroundColor: '#e3e8ef', color: '#0a1e3a' }}>
        <Header currentPage="home" />

        {/* PROFESSIONAL HERO SECTION - MATCHING SCREENSHOT NAVY-GREEN DESIGN */}
        <section className="relative overflow-hidden bg-gradient-to-r from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
          <div className="max-w-7xl mx-auto px-6 lg:px-8 pt-16 pb-20 relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-center">

              {/* Left Side - Content */}
              <div className="lg:col-span-6">
                <h1 className="text-white font-bold text-5xl lg:text-6xl leading-tight mb-6">
                  Warehouse
                  <br />
                  <span className="block">management</span>
                  <br />
                  <span className="block">software.</span>
                </h1>

                <p className="text-gray-300 text-lg leading-relaxed mb-8 max-w-lg">
                  World's simplest and most efficient web-based multi-channel order fulfillment and warehouse management software (WMS).
                </p>

                <div className="flex flex-col sm:flex-row gap-4 mb-12">
                  <button className="bg-[#4caf50] hover:bg-[#3a8e3a] text-white font-semibold rounded-full px-8 py-3 flex items-center justify-center space-x-2 transition-all duration-300 hover:scale-105">
                    <span>GET STARTED</span>
                    <i className="fas fa-arrow-right"></i>
                  </button>

                  <button className="text-white font-medium flex items-center space-x-2 hover:underline">
                    <span>REQUEST DEMO</span>
                    <i className="fas fa-arrow-right"></i>
                  </button>
                </div>
              </div>

              {/* Right Side - Professional Dashboard Cards */}
              <div className="lg:col-span-6 relative">
                <div className="relative h-96 max-w-2xl mx-auto">

                  {/* Main Products Card - Matching Screenshot Design */}
                  <div className="absolute top-8 left-8 w-44 h-44 bg-white/95 backdrop-blur-sm rounded-xl p-6 shadow-2xl border border-white/20">
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-sm font-semibold text-gray-800">Products</span>
                      <span className="text-[#f97316] font-bold text-sm bg-[#f97316]/10 px-2 py-1 rounded">48%</span>
                    </div>
                    <div className="text-4xl font-bold text-gray-900 mb-2">872</div>
                    <div className="text-sm text-gray-600">April</div>
                  </div>

                  {/* Chart Visualization Card - Professional Design */}
                  <div className="absolute top-20 left-56 w-44 h-44 bg-white/95 backdrop-blur-sm rounded-xl p-6 shadow-2xl border border-white/20 flex flex-col items-center justify-center">
                    {/* Professional Chart Visualization */}
                    <div className="w-20 h-20 mb-4 relative">
                      {/* Bar Chart Representation */}
                      <div className="absolute bottom-0 left-2 w-3 h-12 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t"></div>
                      <div className="absolute bottom-0 left-7 w-3 h-16 bg-gradient-to-t from-blue-600 to-blue-500 rounded-t"></div>
                      <div className="absolute bottom-0 left-12 w-3 h-10 bg-gradient-to-t from-blue-400 to-blue-300 rounded-t"></div>
                      <div className="absolute bottom-0 left-17 w-3 h-14 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t"></div>
                      {/* Highlight Point */}
                      <div className="absolute top-2 left-7 w-3 h-3 bg-[#f97316] rounded-full shadow-lg"></div>
                    </div>
                    <div className="text-sm font-semibold text-gray-800">872</div>
                  </div>

                  {/* Professional Arrow Icon */}
                  <div className="absolute top-40 right-20 text-white/90">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>

                  {/* Floating Circular Badge */}
                  <div className="absolute bottom-8 right-8 w-20 h-20 rounded-full border-2 border-white/30 text-white/70 text-xs flex items-center justify-center text-center p-3 backdrop-blur-sm">
                    Warehouse Management Software
                  </div>

                  {/* Security Lock Icon */}
                  <div className="absolute top-4 right-4 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white shadow-lg border border-white/30">
                    <i className="fas fa-lock text-lg"></i>
                  </div>

                </div>
              </div>

            </div>
          </div>
        </section>

        {/* PROFESSIONAL INDUSTRIES SECTION */}
        <section className="bg-white max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-20 rounded-b-3xl shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-12">

            {/* Left Side - Industry Grid */}
            <div className="md:col-span-7 grid grid-cols-2 gap-8">

              {/* Food & Beverage */}
              <div className="group hover:transform hover:scale-105 transition-all duration-300">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-4 h-4 rounded-full bg-[#f97316] shadow-md"></div>
                  <h3 className="text-sm font-semibold text-[#0a1e3a]">Food & Beverage</h3>
                </div>
                <p className="text-xs text-[#7a7a7a] leading-relaxed">
                  Specialized solutions for food safety, temperature control, and compliance tracking in food and beverage warehouses.
                </p>
              </div>

              {/* Home Improvement */}
              <div className="group hover:transform hover:scale-105 transition-all duration-300">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-4 h-4 rounded-full bg-[#f97316] shadow-md"></div>
                  <h3 className="text-sm font-semibold text-[#0a1e3a]">Home Improvement</h3>
                </div>
                <p className="text-xs text-[#7a7a7a] leading-relaxed">
                  Manage large inventory items, seasonal products, and complex SKU variations with ease and precision.
                </p>
              </div>

              {/* Pharmaceutical */}
              <div className="group hover:transform hover:scale-105 transition-all duration-300">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-4 h-4 rounded-full bg-[#f97316] shadow-md"></div>
                  <h3 className="text-sm font-semibold text-[#0a1e3a]">Pharmaceutical</h3>
                </div>
                <p className="text-xs text-[#7a7a7a] leading-relaxed">
                  Ensure regulatory compliance with batch tracking, expiration management, and controlled environment monitoring.
                </p>
              </div>

              {/* Internet Retailers */}
              <div className="group hover:transform hover:scale-105 transition-all duration-300">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-4 h-4 rounded-full bg-[#f97316] shadow-md"></div>
                  <h3 className="text-sm font-semibold text-[#0a1e3a]">Internet Retailers</h3>
                </div>
                <p className="text-xs text-[#7a7a7a] leading-relaxed">
                  Scale your e-commerce operations with multi-channel integration and automated order fulfillment.
                </p>
              </div>

            </div>

            {/* Right Side - Content */}
            <div className="md:col-span-5 flex flex-col justify-center">
              <h2 className="text-3xl font-bold text-[#0a1e3a] mb-4 leading-tight">
                Industries We Service
              </h2>
              <p className="text-sm text-[#7a7a7a] mb-8 leading-relaxed">
                Our paperless WMS (Warehouse Management System) is trusted by leading businesses across the globe with industry-specific features and compliance tools.
              </p>
              <button className="bg-[#4caf50] hover:bg-[#3a8e3a] text-white text-sm font-semibold rounded-full px-8 py-3 w-max shadow-lg transition-all duration-300 hover:scale-105">
                VIEW MORE
              </button>
            </div>

          </div>
        </section>

        {/* PROFESSIONAL INVENTORY FLOW SECTION */}
        <section className="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-20 grid grid-cols-1 md:grid-cols-12 gap-16 items-center">

          {/* Left Side - Content */}
          <div className="md:col-span-6 max-w-lg">
            <h2 className="text-4xl font-bold text-[#0a1e3a] mb-8 leading-tight">
              Inventory Flow Championed
            </h2>

            <ul className="text-sm text-[#7a7a7a] space-y-6 mb-10">
              <li className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full bg-[#f97316] mt-2 flex-shrink-0"></div>
                <span>Store products safely with specialty storage options like cold storage, hazardous material, high value inventory etc.</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full bg-[#f97316] mt-2 flex-shrink-0"></div>
                <span>Periodic stock take and automated replenishments enable you to monitor inventory levels and eliminate stock out events.</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="w-2 h-2 rounded-full bg-[#f97316] mt-2 flex-shrink-0"></div>
                <span>Whether you track your inventory by batch, lot or even serial number, always get 100% accurate audits.</span>
              </li>
            </ul>

            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-[#4caf50] hover:bg-[#3a8e3a] text-white text-sm font-semibold rounded-full px-8 py-3 transition-all duration-300 hover:scale-105">
                SET IT ACTION
              </button>
              <button className="text-sm font-semibold underline text-[#0a1e3a] hover:scale-105 transition-transform duration-300">
                TRY IT FREE
              </button>
            </div>
          </div>

          {/* Right Side - Professional Dashboard */}
          <div className="md:col-span-6 relative bg-gradient-to-r from-[#1a365d] via-[#2d3748] to-[#1a365d] rounded-xl p-8 text-white max-w-md mx-auto md:mx-0 shadow-2xl">

            {/* Overview Section */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">Overview</h3>

              <div className="flex justify-between text-sm mb-2">
                <span className="text-[#4caf50] font-medium">+ 12%</span>
                <span className="text-[#4caf50] font-medium">+ 4.8%</span>
              </div>

              <div className="flex justify-between font-bold text-3xl mb-2">
                <span>$450m</span>
                <span>8.901</span>
              </div>

              <div className="flex justify-between text-sm text-gray-300">
                <span>Total Income</span>
                <span>Total Sales</span>
              </div>
            </div>

            {/* Report Section */}
            <div className="bg-[#1a365d] rounded-lg flex items-center justify-between px-6 py-4 shadow-lg">
              <div className="flex items-center space-x-3">
                <i className="fas fa-chart-bar text-white/80"></i>
                <span className="text-sm">2024</span>
                <span className="text-sm">Report Overview</span>
              </div>
              <button className="bg-[#f97316] hover:bg-[#d05a00] rounded-lg w-10 h-10 flex items-center justify-center text-white shadow-md transition-colors duration-300">
                <i className="fas fa-arrow-right"></i>
              </button>
            </div>

          </div>

        </section>

        {/* PROFESSIONAL COMPANY LOGOS SECTION */}
        <section className="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-16">
          <div className="text-center mb-12">
            <h3 className="text-lg font-semibold text-[#7a7a7a] mb-8">Trusted by Industry Leaders</h3>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-8 lg:gap-16 opacity-70 hover:opacity-100 transition-opacity duration-300">
            <img alt="FedEx Logistics company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/9a5510d9-5102-4789-77bf-391b5a7a6a8c.jpg" width="144"/>
            <img alt="Crane Worldwide Logistics company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/0f431638-d78a-4551-cd41-7644bfec8fae.jpg" width="144"/>
            <img alt="Bollore Logistics company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/4819675f-e061-4a06-2144-05b136d669e9.jpg" width="144"/>
            <img alt="CEVA Logistics company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/c734b295-1592-409b-e4a1-f0fbf795847a.jpg" width="144"/>
            <img alt="HAVI company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/bf5a67af-3d7f-42dc-2374-c55cdec36f40.jpg" width="144"/>
          </div>
        </section>

        {/* PROFESSIONAL MOBILE APP SHOWCASE SECTION */}
        <section className="relative bg-gradient-to-r from-[#1a365d] via-[#2d3748] to-[#1a365d] max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-24 rounded-t-3xl shadow-2xl">

          {/* Floating Arrow Icon */}
          <div className="absolute -top-8 left-1/2 -translate-x-1/2 w-14 h-14 rounded-full bg-[#f97316] flex items-center justify-center text-white text-lg shadow-lg">
            <i className="fas fa-arrow-down"></i>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">

            {/* Left Side - Mobile Mockup */}
            <div className="relative max-w-xs mx-auto lg:mx-0">

              {/* Clock Icon */}
              <div className="absolute top-5 left-5 w-10 h-10 rounded-full bg-white/25 flex items-center justify-center text-white text-lg shadow-md z-10">
                <i className="fas fa-clock"></i>
              </div>

              {/* Mobile Screen */}
              <img alt="Mobile phone showing warehouse management app interface" className="rounded-3xl shadow-2xl" height="560" src="https://storage.googleapis.com/a1aa/image/1347993e-5ce4-4703-cd22-1a5752fe0d69.jpg" width="280"/>

              {/* Floating Badge */}
              <div className="absolute bottom-14 right-14 w-20 h-20 rounded-full border border-white/40 text-white/60 text-xs flex items-center justify-center text-center p-2 shadow-lg">
                Warehouse Management Software
              </div>

              {/* Arrow Down Icon */}
              <div className="absolute bottom-28 right-20 w-10 h-10 rounded-full bg-white/25 flex items-center justify-center text-white text-lg shadow-md">
                <i className="fas fa-arrow-down"></i>
              </div>

            </div>

            {/* Right Side - Content */}
            <div className="text-white">
              <h3 className="text-3xl font-bold mb-8 leading-tight">
                We have a solution for eCommerce challenges of all shapes & sizes across Asia Pacific
              </h3>

              <p className="mb-6 text-gray-300 leading-relaxed">
                Whether you are an eCommerce distributor, an asset-light enabler, an offline retailer transforming to omni-channel, a B2B logistics company trying to start eCommerce fulfillment, or an SME scaling online business, you have your own unique set of challenges.
              </p>

              <p className="mb-8 text-gray-300 leading-relaxed">
                Flexe's cutting-edge technology simplifies backend operations, and helps you achieve unprecedented productivity through automated selling management.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-[#4caf50] hover:bg-[#3a8e3a] text-white font-semibold rounded-full px-8 py-3 transition-all duration-300 hover:scale-105">
                  GET STARTED
                </button>
                <button className="text-white font-medium underline hover:no-underline transition-all duration-300">
                  TRY IT FREE
                </button>
              </div>
            </div>

          </div>

        </section>

        {/* PROFESSIONAL DISCOVER SECTION */}
        <section className="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-24 text-center">

          <h2 className="text-3xl font-bold text-[#0a1e3a] mb-16">
            Discover Who We Are
          </h2>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-5xl mx-auto mb-20">

            {/* Features */}
            <div className="group hover:transform hover:scale-105 transition-all duration-300">
              <div className="mx-auto mb-6 w-16 h-16 rounded-full bg-[#0a1e3a] flex items-center justify-center text-[#f97316] text-2xl shadow-lg group-hover:shadow-xl transition-all duration-300">
                <i className="fas fa-cogs"></i>
              </div>
              <h4 className="text-lg font-semibold text-[#0a1e3a] mb-4">Features</h4>
              <p className="text-sm text-[#7a7a7a] leading-relaxed">
                Ensure maximum output from your warehouse to achieve rising success on both B2B and B2C fronts with Flexe's smart & intuitive features.
              </p>
            </div>

            {/* Industry Solutions */}
            <div className="group hover:transform hover:scale-105 transition-all duration-300">
              <div className="mx-auto mb-6 w-16 h-16 rounded-full bg-[#0a1e3a] flex items-center justify-center text-[#f97316] text-2xl shadow-lg group-hover:shadow-xl transition-all duration-300">
                <i className="fas fa-industry"></i>
              </div>
              <h4 className="text-lg font-semibold text-[#0a1e3a] mb-4">Industry Solutions</h4>
              <p className="text-sm text-[#7a7a7a] leading-relaxed">
                Discover how you can start eCommerce fulfillment, streamline warehouse operations, manage logistics in-house, or solve your logistics challenges.
              </p>
            </div>

            {/* Case Studies */}
            <div className="group hover:transform hover:scale-105 transition-all duration-300">
              <div className="mx-auto mb-6 w-16 h-16 rounded-full bg-[#0a1e3a] flex items-center justify-center text-[#f97316] text-2xl shadow-lg group-hover:shadow-xl transition-all duration-300">
                <i className="fas fa-file-alt"></i>
              </div>
              <h4 className="text-lg font-semibold text-[#0a1e3a] mb-4">Case Studies</h4>
              <p className="text-sm text-[#7a7a7a] leading-relaxed">
                Find out how multiple global players such as Nestle, Luxasia, Zilingo & more have defined their transformational journeys with Flexe.
              </p>
            </div>

          </div>

          {/* Divider */}
          <hr className="border-t border-[#e3e8ef] mb-16"/>

          {/* Professional Testimonial Section */}
          <div className="max-w-2xl mx-auto">
            <div className="w-20 h-20 rounded-full bg-[#0a1e3a] mx-auto mb-8 flex items-center justify-center text-[#f97316] text-3xl shadow-lg">
              <i className="fas fa-quote-left"></i>
            </div>

            <blockquote className="text-xl text-[#0a1e3a] mb-6 leading-relaxed font-medium">
              "We found the Inventory app to be a powerful tool to better organize our warehouse. It's simple and easily customizable."
            </blockquote>

            <p className="text-[#f97316] font-semibold mb-12">
              Mario Riva, COO
            </p>

            {/* Navigation Buttons */}
            <div className="flex justify-center space-x-8">
              <button className="w-12 h-12 rounded-full border-2 border-[#0a1e3a] text-[#0a1e3a] flex items-center justify-center hover:bg-[#0a1e3a] hover:text-white transition-all duration-300 shadow-md">
                <i className="fas fa-arrow-left"></i>
              </button>
              <button className="w-12 h-12 rounded-full border-2 border-[#0a1e3a] text-[#0a1e3a] flex items-center justify-center hover:bg-[#0a1e3a] hover:text-white transition-all duration-300 shadow-md">
                <i className="fas fa-arrow-right"></i>
              </button>
            </div>
          </div>

        </section>

        <Footer />
      </div>
    </>
  );
}
