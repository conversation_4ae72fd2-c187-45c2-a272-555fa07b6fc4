'use client';

import { useEffect } from 'react';
import Header from './components/Header';
import Footer from './components/Footer';

export default function Home() {
  // Intersection Observer for scroll-triggered animations
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in-view');
          // Only animate once
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe all elements with scroll-animate class
    const animateElements = document.querySelectorAll('.scroll-animate');
    animateElements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);
  return (
    <>
      <div style={{ backgroundColor: '#e3e8ef', color: '#0a1e3a' }}>
        <Header currentPage="home" />

        {/* HERO SECTION - REDESIGNED WITH ANIMATIONS */}
        <section className="relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0a1e3a 0%, #1a2f4a 50%, #2a4a6a 100%)' }}>
          <div className="max-w-7xl mx-auto px-6 lg:px-8 pt-20 pb-24 relative z-10">
            <div className="text-center">

              {/* Main Heading with Staggered Animation */}
              <div className="mb-8">
                <h1 className="text-white font-bold text-5xl lg:text-6xl xl:text-7xl leading-tight animate-fade-in-up">
                  <span className="inline-block animate-slide-in-left" style={{ animationDelay: '0.2s' }}>
                    Modern Warehouse
                  </span>
                  <span className="block animate-slide-in-right" style={{ color: '#4caf50', animationDelay: '0.4s' }}>
                    Management
                  </span>
                  <span className="block animate-slide-in-left" style={{ animationDelay: '0.6s' }}>
                    Solutions
                  </span>
                </h1>
              </div>

              {/* Subtitle with Fade Animation */}
              <p className="text-gray-300 text-xl lg:text-2xl leading-relaxed mb-12 max-w-4xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
                Streamline your operations with our comprehensive warehouse management platform.
                Optimize inventory, reduce costs, and boost efficiency with cutting-edge technology.
              </p>

              {/* CTA Buttons with Bounce Animation */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16 animate-fade-in-up" style={{ animationDelay: '1s' }}>
                <button
                  className="text-white text-lg font-semibold rounded-lg px-10 py-4 flex items-center justify-center space-x-3 shadow-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl animate-pulse-subtle"
                  style={{ backgroundColor: '#4caf50' }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#3a8e3a';
                    e.target.style.transform = 'scale(1.05) translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = '#4caf50';
                    e.target.style.transform = 'scale(1) translateY(0)';
                  }}
                  type="button"
                >
                  <span>Get Started Free</span>
                  <i className="fas fa-arrow-right animate-bounce-x"></i>
                </button>

                <button className="border-2 border-white/30 text-white text-lg font-semibold rounded-lg px-10 py-4 hover:bg-white/10 hover:border-white/50 transition-all duration-300 transform hover:scale-105" type="button">
                  Watch Demo
                </button>
              </div>

              {/* Stats Cards with Staggered Animation */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">

                {/* Stat 1 */}
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-xl animate-slide-up hover:shadow-2xl hover:scale-105 transition-all duration-300" style={{ animationDelay: '1.2s' }}>
                  <div className="text-4xl font-bold text-white mb-2 animate-count-up">99.9%</div>
                  <div className="text-gray-300 text-lg animate-fade-in" style={{ animationDelay: '1.4s' }}>System Uptime</div>
                  <div className="text-sm text-gray-400 mt-2 animate-fade-in" style={{ animationDelay: '1.6s' }}>Reliable & Secure</div>
                </div>

                {/* Stat 2 */}
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-xl animate-slide-up hover:shadow-2xl hover:scale-105 transition-all duration-300" style={{ animationDelay: '1.4s' }}>
                  <div className="text-4xl font-bold mb-2 animate-count-up" style={{ color: '#4caf50' }}>50K+</div>
                  <div className="text-gray-300 text-lg animate-fade-in" style={{ animationDelay: '1.6s' }}>Orders Daily</div>
                  <div className="text-sm text-gray-400 mt-2 animate-fade-in" style={{ animationDelay: '1.8s' }}>High Performance</div>
                </div>

                {/* Stat 3 */}
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-xl animate-slide-up hover:shadow-2xl hover:scale-105 transition-all duration-300" style={{ animationDelay: '1.6s' }}>
                  <div className="text-4xl font-bold mb-2 animate-count-up" style={{ color: '#f97316' }}>500+</div>
                  <div className="text-gray-300 text-lg animate-fade-in" style={{ animationDelay: '1.8s' }}>Enterprise Clients</div>
                  <div className="text-sm text-gray-400 mt-2 animate-fade-in" style={{ animationDelay: '2s' }}>Trusted Worldwide</div>
                </div>

              </div>

            </div>
          </div>
        </section>

        {/* INDUSTRIES SECTION - EXACT REPLICATION OF INDEX.HTML */}
        <section className="bg-white max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-20 rounded-b-3xl shadow-lg card-shadow">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-12">

            {/* Left Side - Industry Grid with Scroll Animations */}
            <div className="md:col-span-7 grid grid-cols-2 gap-10">

              {/* Food & Beverage */}
              <div className="scroll-animate scroll-fade-in-left" data-delay="0">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-4 h-4 rounded-full shadow-md animate-pulse-dot" style={{ backgroundColor: '#f97316', boxShadow: '0 4px 6px rgba(249, 115, 22, 0.5)' }}></div>
                  <h3 className="text-sm font-semibold tracking-wide" style={{ color: '#0a1e3a' }}>Food & Beverage</h3>
                </div>
                <p className="text-[10px] leading-tight tracking-wide" style={{ color: '#7a7a7a' }}>
                  Erat condimentum consectetur dignissim convallis imperdiet nunc risus facilisi.
                </p>
              </div>

              {/* Home Improvement */}
              <div className="scroll-animate scroll-fade-in-right" data-delay="100">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-4 h-4 rounded-full shadow-md animate-pulse-dot" style={{ backgroundColor: '#f97316', boxShadow: '0 4px 6px rgba(249, 115, 22, 0.5)' }}></div>
                  <h3 className="text-sm font-semibold tracking-wide" style={{ color: '#0a1e3a' }}>Home Improvement</h3>
                </div>
                <p className="text-[10px] leading-tight tracking-wide" style={{ color: '#7a7a7a' }}>
                  Erat condimentum consectetur dignissim convallis imperdiet nunc risus facilisi.
                </p>
              </div>

              {/* Pharmaceutical */}
              <div className="scroll-animate scroll-fade-in-left" data-delay="200">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-4 h-4 rounded-full shadow-md animate-pulse-dot" style={{ backgroundColor: '#f97316', boxShadow: '0 4px 6px rgba(249, 115, 22, 0.5)' }}></div>
                  <h3 className="text-sm font-semibold tracking-wide" style={{ color: '#0a1e3a' }}>Pharmaceutical</h3>
                </div>
                <p className="text-[10px] leading-tight tracking-wide" style={{ color: '#7a7a7a' }}>
                  Erat condimentum consectetur dignissim convallis imperdiet nunc risus facilisi.
                </p>
              </div>

              {/* Internet Retailers */}
              <div className="scroll-animate scroll-fade-in-right" data-delay="300">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-4 h-4 rounded-full shadow-md animate-pulse-dot" style={{ backgroundColor: '#f97316', boxShadow: '0 4px 6px rgba(249, 115, 22, 0.5)' }}></div>
                  <h3 className="text-sm font-semibold tracking-wide" style={{ color: '#0a1e3a' }}>Internet Retailers</h3>
                </div>
                <p className="text-[10px] leading-tight tracking-wide" style={{ color: '#7a7a7a' }}>
                  Erat condimentum consectetur dignissim convallis imperdiet nunc risus facilisi.
                </p>
              </div>

            </div>

            {/* Right Side - Content with Scroll Animation */}
            <div className="md:col-span-5 flex flex-col justify-center">
              <h2 className="text-3xl font-serif font-normal mb-3 tracking-tight scroll-animate scroll-slide-in-right">
                Industries We Services
              </h2>
              <p className="text-sm mb-8 leading-relaxed tracking-wide scroll-animate scroll-fade-in-up" data-delay="200" style={{ color: '#7a7a7a' }}>
                Paperless WMS (Warehouse Management System) is trusted by leading businesses across the globe with features suited.
              </p>
              <button
                className="text-white text-sm font-semibold rounded-full px-8 py-3 w-max shadow-lg card-shadow transition-all duration-300 hover:scale-105 scroll-animate scroll-bounce-in"
                data-delay="400"
                style={{ backgroundColor: '#4caf50' }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#3a8e3a';
                  e.target.style.transform = 'scale(1.05)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#4caf50';
                  e.target.style.transform = 'scale(1)';
                }}
                type="button"
              >
                VIEW MORE
              </button>
            </div>

          </div>
        </section>

        {/* INVENTORY FLOW SECTION - EXACT REPLICATION OF INDEX.HTML */}
        <section className="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-20 grid grid-cols-1 md:grid-cols-12 gap-16 items-center">

          {/* Left Side - Content with Scroll Animation */}
          <div className="md:col-span-6 max-w-lg">
            <h2 className="text-4xl font-serif font-normal mb-8 leading-tight tracking-tight scroll-animate scroll-slide-in-right">
              Inventory Flow Championed
            </h2>

            <ul className="text-sm space-y-4 mb-10 list-disc list-inside tracking-wide" style={{ color: '#7a7a7a' }}>
              <li style={{ listStyleColor: '#f97316' }} className="scroll-animate scroll-fade-in-left" data-delay="100">
                Store products safely with specialty storage options like cold storage, hazardous material, high value inventory etc.
              </li>
              <li style={{ listStyleColor: '#f97316' }} className="scroll-animate scroll-fade-in-left" data-delay="200">
                Periodic stock take and automated replenishments enable you to monitor inventory levels and eliminate stock out events.
              </li>
              <li style={{ listStyleColor: '#f97316' }} className="scroll-animate scroll-fade-in-left" data-delay="300">
                Whether you track your inventory by batch, lot or even serial number, always get 100% accurate audits.
              </li>
            </ul>

            <div className="flex space-x-6">
              <button
                className="text-white text-sm font-semibold rounded-full px-8 py-3 flex items-center space-x-3 shadow-lg card-shadow transition-all duration-300 hover:scale-105 scroll-animate scroll-bounce-in"
                data-delay="400"
                style={{ backgroundColor: '#4caf50' }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#3a8e3a';
                  e.target.style.transform = 'scale(1.05)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#4caf50';
                  e.target.style.transform = 'scale(1)';
                }}
                type="button"
              >
                <span>SET IT ACTION</span>
              </button>
              <button className="text-sm font-semibold underline tracking-wide hover:scale-105 transition-transform duration-300 scroll-animate scroll-fade-in-up" data-delay="500" style={{ color: '#0a1e3a' }} type="button">
                TRY IT FREE
              </button>
            </div>
          </div>

          {/* Right Side - Dashboard - EXACT REPLICATION */}
          <div className="md:col-span-6 relative rounded-xl p-8 text-white max-w-md mx-auto md:mx-0 card-shadow" style={{ background: 'linear-gradient(to right, #0a1e3a, #0a2e2a, #2a5a3a)' }}>

            {/* Overview Section - EXACT REPLICATION */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 tracking-wide">Overview</h3>

              <div className="flex justify-between text-sm font-light mb-2 tracking-wide">
                <span style={{ color: '#4caf50' }}>+ 12%</span>
                <span style={{ color: '#4caf50' }}>+ 4.8%</span>
              </div>

              <div className="flex justify-between font-extrabold text-4xl mb-2 tracking-tight" style={{ fontFeatureSettings: "'tnum'" }}>
                <span>$450m</span>
                <span>8.901</span>
              </div>

              <div className="flex justify-between text-sm font-light tracking-wide">
                <span>Total Income</span>
                <span>Total Sales</span>
              </div>
            </div>

            {/* Report Section - EXACT REPLICATION */}
            <div className="rounded-lg flex items-center justify-between px-6 py-3 text-sm font-light cursor-pointer select-none shadow-lg" style={{ backgroundColor: '#0a1e3a' }}>
              <div className="flex items-center space-x-3">
                <i className="fas fa-comment-alt text-white/80 text-base"></i>
                <span>2022</span>
                <span>Report Overview</span>
              </div>
              <button
                aria-label="Next"
                className="rounded-lg w-10 h-10 flex items-center justify-center text-white shadow-md transition"
                style={{ backgroundColor: '#f97316' }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#d05a00'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#f97316'}
              >
                <i className="fas fa-arrow-right"></i>
              </button>
            </div>

          </div>

        </section>

        {/* LOGOS SECTION - EXACT REPLICATION OF INDEX.HTML */}
        <section className="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-16 flex justify-center space-x-16">
          <img alt="FedEx Logistics company logo in blue and orange" className="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/9a5510d9-5102-4789-77bf-391b5a7a6a8c.jpg" width="144"/>
          <img alt="Crane Worldwide Logistics company logo in green and blue" className="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/0f431638-d78a-4551-cd41-7644bfec8fae.jpg" width="144"/>
          <img alt="Bollore Logistics company logo with red and blue text and arrow" className="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/4819675f-e061-4a06-2144-05b136d669e9.jpg" width="144"/>
          <img alt="CEVA Logistics company logo with red and black text" className="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/c734b295-1592-409b-e4a1-f0fbf795847a.jpg" width="144"/>
          <img alt="HAVI company logo with green and blue bars and text" className="h-12 w-auto drop-shadow-lg" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/bf5a67af-3d7f-42dc-2374-c55cdec36f40.jpg" width="144"/>
        </section>

        {/* E-COMMERCE SOLUTION SECTION - EXACT REPLICATION OF INDEX.HTML */}
        <section className="relative max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-24 flex flex-col md:flex-row items-center md:items-start gap-16 md:gap-24 rounded-t-3xl shadow-lg card-shadow" style={{ background: 'linear-gradient(to right, #0a1e3a, #0a2e2a, #2a5a3a)' }}>

          {/* Floating Arrow Icon - EXACT REPLICATION */}
          <div className="absolute -top-8 left-1/2 -translate-x-1/2 w-14 h-14 rounded-full flex items-center justify-center text-white text-lg font-light cursor-default select-none z-10 shadow-lg" style={{ backgroundColor: '#f97316' }}>
            <i className="fas fa-arrow-down"></i>
          </div>

          {/* Left Side - Mobile Mockup - EXACT REPLICATION */}
          <div className="relative max-w-xs md:max-w-[300px] shadow-2xl rounded-3xl overflow-hidden">

            {/* Clock Icon - EXACT REPLICATION */}
            <div className="absolute top-5 left-5 w-10 h-10 rounded-full bg-white/25 flex items-center justify-center text-white text-lg cursor-default select-none shadow-md" title="Clock icon">
              <i className="fas fa-clock"></i>
            </div>

            {/* Mobile Screen - EXACT REPLICATION */}
            <img alt="Mobile phone screen showing inventory app dashboard with charts and stats" className="rounded-3xl" height="560" src="https://storage.googleapis.com/a1aa/image/1347993e-5ce4-4703-cd22-1a5752fe0d69.jpg" width="280"/>

            {/* Floating Badge - EXACT REPLICATION */}
            <div className="absolute bottom-14 right-14 w-20 h-20 rounded-full border border-white/40 text-white/60 text-[11px] font-light flex items-center justify-center text-center cursor-default select-none tracking-wide card-shadow" style={{ fontFeatureSettings: "'tnum'" }}>
              Warehouse Management Software
            </div>

            {/* Arrow Down Icon - EXACT REPLICATION */}
            <div className="absolute bottom-28 right-20 w-10 h-10 rounded-full bg-white/25 flex items-center justify-center text-white text-lg cursor-default select-none shadow-md" title="Arrow down icon">
              <i className="fas fa-arrow-down"></i>
            </div>

          </div>

          {/* Right Side - Content - EXACT REPLICATION */}
          <div className="max-w-lg text-white text-base leading-relaxed tracking-wide">
            <h3 className="text-2xl font-serif font-normal mb-8 tracking-tight">
              We have a solution for eCommerce challenges of all shapes & sizes across Asia Pacific
            </h3>

            <p className="mb-10 text-sm leading-relaxed tracking-wide">
              Whether you are an eCommerce distributor, an asset-light enabler, an offline retailer transforming to omni-channel, a B2B logistics company trying to start eCommerce fulfillment, or an SME scaling online business, you have your own unique set of challenges.
            </p>

            <p className="mb-10 text-sm leading-relaxed tracking-wide">
              Anchanto's cutting-edge technology simplifies backend operations, and helps you achieve unprecedented productivity through automated selling management.
            </p>

            <div className="flex space-x-6">
              <button
                className="text-white text-sm font-semibold rounded-full px-8 py-3 flex items-center space-x-3 shadow-lg card-shadow transition"
                style={{ backgroundColor: '#4caf50' }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#3a8e3a'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#4caf50'}
                type="button"
              >
                <span>SET IT ACTION</span>
              </button>
              <button className="text-sm font-semibold underline text-white tracking-wide" type="button">
                TRY IT FREE
              </button>
            </div>
          </div>

        </section>

        {/* DISCOVER SECTION WITH SCROLL ANIMATIONS */}
        <section className="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-24 text-center">

          <h2 className="text-3xl font-serif font-normal mb-24 tracking-tight scroll-animate scroll-fade-in-up">
            Discover Who We Are
          </h2>

          {/* Features Grid with Scroll Staggered Animation */}
          <div className="flex flex-col md:flex-row justify-center gap-16 max-w-5xl mx-auto mb-24">

            {/* Features */}
            <div className="max-w-xs scroll-animate scroll-zoom-in hover-lift" data-delay="0">
              <div className="mx-auto mb-6 w-14 h-14 rounded-full flex items-center justify-center text-2xl cursor-default select-none shadow-lg transition-all duration-300 hover:scale-110 scroll-animate scroll-rotate-in" data-delay="100" style={{ backgroundColor: '#0a1e3a', color: '#f97316' }}>
                <i className="fas fa-cogs"></i>
              </div>
              <h4 className="text-sm font-semibold mb-3 tracking-wide scroll-animate scroll-fade-in-up" data-delay="200">Features</h4>
              <p className="text-[11px] leading-tight tracking-wide scroll-animate scroll-fade-in-up" data-delay="300" style={{ color: '#7a7a7a' }}>
                Ensure maximum output from your warehouse to achieve rising success on both B2B and B2C fronts with Wareo's smart & intuitive features.
              </p>
            </div>

            {/* Industry Solutions */}
            <div className="max-w-xs scroll-animate scroll-zoom-in hover-lift" data-delay="100">
              <div className="mx-auto mb-6 w-14 h-14 rounded-full flex items-center justify-center text-2xl cursor-default select-none shadow-lg transition-all duration-300 hover:scale-110 scroll-animate scroll-rotate-in" data-delay="200" style={{ backgroundColor: '#0a1e3a', color: '#f97316' }}>
                <i className="fas fa-industry"></i>
              </div>
              <h4 className="text-sm font-semibold mb-3 tracking-wide scroll-animate scroll-fade-in-up" data-delay="300">Industry Solutions</h4>
              <p className="text-[11px] leading-tight tracking-wide scroll-animate scroll-fade-in-up" data-delay="400" style={{ color: '#7a7a7a' }}>
                Discover how you can start eCommerce fulfillment, streamline warehouse operations, manage logistics in-house, or solve your logistics challenges.
              </p>
            </div>

            {/* Case Studies */}
            <div className="max-w-xs scroll-animate scroll-zoom-in hover-lift" data-delay="200">
              <div className="mx-auto mb-6 w-14 h-14 rounded-full flex items-center justify-center text-2xl cursor-default select-none shadow-lg transition-all duration-300 hover:scale-110 scroll-animate scroll-rotate-in" data-delay="300" style={{ backgroundColor: '#0a1e3a', color: '#f97316' }}>
                <i className="fas fa-file-alt"></i>
              </div>
              <h4 className="text-sm font-semibold mb-3 tracking-wide scroll-animate scroll-fade-in-up" data-delay="400">Case Studies</h4>
              <p className="text-[11px] leading-tight tracking-wide scroll-animate scroll-fade-in-up" data-delay="500" style={{ color: '#7a7a7a' }}>
                Find out how multiple global players such as Nestle, Luxasia, Zilingo & more have defined their transformational journeys with Wareo.
              </p>
            </div>

          </div>

          {/* Divider - EXACT REPLICATION */}
          <hr className="border-t mb-16" style={{ borderColor: '#e3e8ef' }}/>

          {/* Testimonial Section - EXACT REPLICATION */}
          <div className="max-w-xl mx-auto">
            <div className="w-20 h-20 rounded-full mx-auto mb-8 flex items-center justify-center text-4xl cursor-default select-none shadow-lg" style={{ backgroundColor: '#0a1e3a', color: '#f97316' }}>
              <i className="fas fa-quote-left"></i>
            </div>

            <p className="text-lg mb-6 leading-relaxed tracking-wide" style={{ color: '#0a1e3a' }}>
              We found the Inventory app to be a powerful tool to better organize our warehouse. It's simple and easily customizable.
            </p>

            <p className="text-sm font-semibold mb-12 tracking-wide" style={{ color: '#f97316' }}>
              Mario Riva, COO
            </p>

            {/* Navigation Buttons - EXACT REPLICATION */}
            <div className="flex justify-center space-x-16 text-sm font-light" style={{ color: '#0a1e3a' }}>
              <button
                aria-label="Previous testimonial"
                className="rounded-full w-10 h-10 flex items-center justify-center transition shadow-md"
                style={{ border: '1px solid #0a1e3a' }}
                onMouseEnter={(e) => { e.target.style.backgroundColor = '#0a1e3a'; e.target.style.color = 'white'; }}
                onMouseLeave={(e) => { e.target.style.backgroundColor = 'transparent'; e.target.style.color = '#0a1e3a'; }}
                type="button"
              >
                <i className="fas fa-arrow-left"></i>
              </button>
              <button
                aria-label="Next testimonial"
                className="rounded-full w-10 h-10 flex items-center justify-center transition shadow-md"
                style={{ border: '1px solid #0a1e3a' }}
                onMouseEnter={(e) => { e.target.style.backgroundColor = '#0a1e3a'; e.target.style.color = 'white'; }}
                onMouseLeave={(e) => { e.target.style.backgroundColor = 'transparent'; e.target.style.color = '#0a1e3a'; }}
                type="button"
              >
                <i className="fas fa-arrow-right"></i>
              </button>
            </div>
          </div>

        </section>

        <Footer />
      </div>
    </>
  );
}
