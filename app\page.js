'use client';

import { useEffect } from 'react';
import Header from './components/Header';
import Footer from './components/Footer';

export default function Home() {
  // Intersection Observer for scroll-triggered animations
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in-view');
          // Only animate once
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe all elements with scroll-animate class
    const animateElements = document.querySelectorAll('.scroll-animate');
    animateElements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);
  return (
    <>
      <div style={{ backgroundColor: '#e3e8ef', color: '#0a1e3a' }}>
        <Header currentPage="home" />

        {/* ENHANCED HERO SECTION WITH IMPROVED COLOR THEME */}
        <section className="relative overflow-hidden bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a]">
          {/* Enhanced Background Pattern */}
          <div className="absolute inset-0">
            {/* Gradient Overlays */}
            <div className="absolute inset-0 bg-gradient-to-t from-[#0a1e3a]/50 to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-[#2a5a3a]/30 via-transparent to-[#0a2e2a]/30"></div>

            {/* Decorative Elements */}
            <div className="absolute top-20 left-20 w-32 h-32 border-2 border-[#2a5a3a]/40 rounded-full animate-pulse"></div>
            <div className="absolute bottom-20 right-20 w-24 h-24 border-2 border-[#0a2e2a]/40 rounded-full animate-pulse"></div>
            <div className="absolute top-1/2 left-1/3 w-16 h-16 border-2 border-[#0a1e3a]/40 rounded-full animate-pulse"></div>

            {/* Floating Particles */}
            <div className="absolute top-32 right-1/4 w-3 h-3 bg-[#2a5a3a] rounded-full opacity-60 animate-bounce"></div>
            <div className="absolute bottom-32 left-1/4 w-2 h-2 bg-[#0a2e2a] rounded-full opacity-60 animate-bounce" style={{ animationDelay: '1s' }}></div>
          </div>

          <div className="max-w-7xl mx-auto px-6 lg:px-8 pt-24 pb-32 relative z-10">
            <div className="text-center max-w-5xl mx-auto">

              {/* Enhanced Main Heading */}
              <h1 className="text-white font-bold text-6xl lg:text-7xl xl:text-8xl leading-tight mb-8">
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-white via-gray-100 to-[#2a5a3a]">Warehouse</span>
                <br />
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-[#2a5a3a] via-white to-[#0a2e2a]">management</span>
                <br />
                <span className="block text-white drop-shadow-2xl">software.</span>
              </h1>

              {/* Enhanced Subtitle */}
              <p className="text-gray-200 text-xl lg:text-2xl leading-relaxed mb-12 max-w-3xl mx-auto drop-shadow-lg">
                World's simplest and most efficient web-based multi-channel order fulfillment and warehouse management software (WMS).
              </p>

              {/* Enhanced CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                <button className="bg-gradient-to-r from-[#2a5a3a] to-[#0a2e2a] hover:from-[#0a2e2a] hover:to-[#2a5a3a] text-white font-semibold text-lg rounded-full px-12 py-4 flex items-center justify-center space-x-3 shadow-2xl transition-all duration-500 hover:scale-105 hover:shadow-3xl border border-[#2a5a3a]/50">
                  <span>GET STARTED</span>
                  <i className="fas fa-arrow-right"></i>
                </button>

                <button className="border-2 border-[#2a5a3a]/60 bg-[#0a1e3a]/30 backdrop-blur-sm text-white font-semibold text-lg rounded-full px-12 py-4 flex items-center justify-center space-x-3 hover:bg-[#2a5a3a]/20 hover:border-[#2a5a3a] transition-all duration-500 hover:scale-105">
                  <span>REQUEST DEMO</span>
                  <i className="fas fa-play"></i>
                </button>
              </div>

              {/* Enhanced Stats Section */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">

                {/* Stat 1 */}
                <div className="bg-gradient-to-br from-[#0a1e3a]/40 to-[#0a2e2a]/40 backdrop-blur-lg rounded-2xl p-8 border border-[#2a5a3a]/30 shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-500 group">
                  <div className="text-4xl font-bold text-white mb-2 group-hover:text-[#2a5a3a] transition-colors duration-300">99.9%</div>
                  <div className="text-gray-200 text-lg">System Uptime</div>
                  <div className="text-sm text-gray-300 mt-2">Reliable & Secure</div>
                  <div className="absolute top-4 right-4 w-3 h-3 bg-[#2a5a3a] rounded-full animate-pulse"></div>
                </div>

                {/* Stat 2 */}
                <div className="bg-gradient-to-br from-[#0a2e2a]/40 to-[#2a5a3a]/40 backdrop-blur-lg rounded-2xl p-8 border border-[#2a5a3a]/30 shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-500 group">
                  <div className="text-4xl font-bold text-[#2a5a3a] mb-2 group-hover:text-white transition-colors duration-300">50K+</div>
                  <div className="text-gray-200 text-lg">Orders Daily</div>
                  <div className="text-sm text-gray-300 mt-2">High Performance</div>
                  <div className="absolute top-4 right-4 w-3 h-3 bg-[#0a2e2a] rounded-full animate-pulse"></div>
                </div>

                {/* Stat 3 */}
                <div className="bg-gradient-to-br from-[#2a5a3a]/40 to-[#0a1e3a]/40 backdrop-blur-lg rounded-2xl p-8 border border-[#2a5a3a]/30 shadow-2xl hover:shadow-3xl hover:scale-105 transition-all duration-500 group">
                  <div className="text-4xl font-bold text-[#0a2e2a] mb-2 group-hover:text-white transition-colors duration-300">500+</div>
                  <div className="text-gray-200 text-lg">Enterprise Clients</div>
                  <div className="text-sm text-gray-300 mt-2">Trusted Worldwide</div>
                  <div className="absolute top-4 right-4 w-3 h-3 bg-[#0a1e3a] rounded-full animate-pulse"></div>
                </div>

              </div>

            </div>
          </div>
        </section>

        {/* ENHANCED INDUSTRIES SECTION WITH THEME COLORS */}
        <section className="bg-gradient-to-br from-white via-gray-50 to-[#0a1e3a]/5 max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-20 rounded-b-3xl shadow-2xl border-t-4 border-[#2a5a3a]">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-12">

            {/* Left Side - Enhanced Industry Grid */}
            <div className="md:col-span-7 grid grid-cols-2 gap-8">

              {/* Food & Beverage */}
              <div className="group hover:transform hover:scale-105 transition-all duration-500 bg-gradient-to-br from-[#0a1e3a]/5 to-[#0a2e2a]/5 p-6 rounded-xl border border-[#2a5a3a]/20 hover:border-[#2a5a3a]/40 hover:shadow-xl">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-5 h-5 rounded-full bg-gradient-to-r from-[#2a5a3a] to-[#0a2e2a] shadow-lg animate-pulse"></div>
                  <h3 className="text-sm font-bold text-[#0a1e3a] group-hover:text-[#2a5a3a] transition-colors duration-300">Food & Beverage</h3>
                </div>
                <p className="text-xs text-[#0a1e3a]/70 leading-relaxed group-hover:text-[#0a1e3a] transition-colors duration-300">
                  Specialized solutions for food safety, temperature control, and compliance tracking in food and beverage warehouses.
                </p>
              </div>

              {/* Home Improvement */}
              <div className="group hover:transform hover:scale-105 transition-all duration-500 bg-gradient-to-br from-[#0a2e2a]/5 to-[#2a5a3a]/5 p-6 rounded-xl border border-[#2a5a3a]/20 hover:border-[#2a5a3a]/40 hover:shadow-xl">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-5 h-5 rounded-full bg-gradient-to-r from-[#0a2e2a] to-[#2a5a3a] shadow-lg animate-pulse"></div>
                  <h3 className="text-sm font-bold text-[#0a1e3a] group-hover:text-[#0a2e2a] transition-colors duration-300">Home Improvement</h3>
                </div>
                <p className="text-xs text-[#0a1e3a]/70 leading-relaxed group-hover:text-[#0a1e3a] transition-colors duration-300">
                  Manage large inventory items, seasonal products, and complex SKU variations with ease and precision.
                </p>
              </div>

              {/* Pharmaceutical */}
              <div className="group hover:transform hover:scale-105 transition-all duration-500 bg-gradient-to-br from-[#2a5a3a]/5 to-[#0a1e3a]/5 p-6 rounded-xl border border-[#2a5a3a]/20 hover:border-[#2a5a3a]/40 hover:shadow-xl">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-5 h-5 rounded-full bg-gradient-to-r from-[#2a5a3a] to-[#0a1e3a] shadow-lg animate-pulse"></div>
                  <h3 className="text-sm font-bold text-[#0a1e3a] group-hover:text-[#2a5a3a] transition-colors duration-300">Pharmaceutical</h3>
                </div>
                <p className="text-xs text-[#0a1e3a]/70 leading-relaxed group-hover:text-[#0a1e3a] transition-colors duration-300">
                  Ensure regulatory compliance with batch tracking, expiration management, and controlled environment monitoring.
                </p>
              </div>

              {/* Internet Retailers */}
              <div className="group hover:transform hover:scale-105 transition-all duration-500 bg-gradient-to-br from-[#0a1e3a]/5 to-[#2a5a3a]/5 p-6 rounded-xl border border-[#2a5a3a]/20 hover:border-[#2a5a3a]/40 hover:shadow-xl">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-5 h-5 rounded-full bg-gradient-to-r from-[#0a1e3a] to-[#0a2e2a] shadow-lg animate-pulse"></div>
                  <h3 className="text-sm font-bold text-[#0a1e3a] group-hover:text-[#0a2e2a] transition-colors duration-300">Internet Retailers</h3>
                </div>
                <p className="text-xs text-[#0a1e3a]/70 leading-relaxed group-hover:text-[#0a1e3a] transition-colors duration-300">
                  Scale your e-commerce operations with multi-channel integration and automated order fulfillment.
                </p>
              </div>

            </div>

            {/* Right Side - Enhanced Content */}
            <div className="md:col-span-5 flex flex-col justify-center bg-gradient-to-br from-[#0a1e3a]/10 to-[#2a5a3a]/10 p-8 rounded-2xl border border-[#2a5a3a]/20">
              <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#0a1e3a] to-[#2a5a3a] mb-6 leading-tight">
                Industries We Service
              </h2>
              <p className="text-sm text-[#0a1e3a]/80 mb-8 leading-relaxed">
                Our paperless WMS (Warehouse Management System) is trusted by leading businesses across the globe with industry-specific features and compliance tools.
              </p>
              <button className="bg-gradient-to-r from-[#2a5a3a] to-[#0a2e2a] hover:from-[#0a2e2a] hover:to-[#2a5a3a] text-white text-sm font-semibold rounded-full px-8 py-3 w-max shadow-xl transition-all duration-500 hover:scale-105 hover:shadow-2xl border border-[#2a5a3a]/30">
                VIEW MORE
              </button>
            </div>

          </div>
        </section>

        {/* ENHANCED INVENTORY FLOW SECTION WITH THEME COLORS */}
        <section className="bg-gradient-to-br from-[#0a1e3a]/5 via-white to-[#2a5a3a]/5 max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-20 grid grid-cols-1 md:grid-cols-12 gap-16 items-center rounded-3xl shadow-2xl border border-[#2a5a3a]/10">

          {/* Left Side - Enhanced Content */}
          <div className="md:col-span-6 max-w-lg">
            <h2 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a] mb-8 leading-tight">
              Inventory Flow Championed
            </h2>

            <ul className="text-sm text-[#0a1e3a]/80 space-y-6 mb-10">
              <li className="flex items-start space-x-4 group hover:transform hover:translateX-2 transition-all duration-300">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-[#2a5a3a] to-[#0a2e2a] mt-2 flex-shrink-0 shadow-lg group-hover:scale-125 transition-transform duration-300"></div>
                <span className="group-hover:text-[#0a1e3a] transition-colors duration-300">Store products safely with specialty storage options like cold storage, hazardous material, high value inventory etc.</span>
              </li>
              <li className="flex items-start space-x-4 group hover:transform hover:translateX-2 transition-all duration-300">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-[#0a2e2a] to-[#2a5a3a] mt-2 flex-shrink-0 shadow-lg group-hover:scale-125 transition-transform duration-300"></div>
                <span className="group-hover:text-[#0a1e3a] transition-colors duration-300">Periodic stock take and automated replenishments enable you to monitor inventory levels and eliminate stock out events.</span>
              </li>
              <li className="flex items-start space-x-4 group hover:transform hover:translateX-2 transition-all duration-300">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-[#2a5a3a] to-[#0a1e3a] mt-2 flex-shrink-0 shadow-lg group-hover:scale-125 transition-transform duration-300"></div>
                <span className="group-hover:text-[#0a1e3a] transition-colors duration-300">Whether you track your inventory by batch, lot or even serial number, always get 100% accurate audits.</span>
              </li>
            </ul>

            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-gradient-to-r from-[#2a5a3a] to-[#0a2e2a] hover:from-[#0a2e2a] hover:to-[#2a5a3a] text-white text-sm font-semibold rounded-full px-8 py-3 transition-all duration-500 hover:scale-105 shadow-xl hover:shadow-2xl border border-[#2a5a3a]/30">
                SET IT ACTION
              </button>
              <button className="text-sm font-semibold underline text-[#0a1e3a] hover:text-[#2a5a3a] hover:scale-105 transition-all duration-300">
                TRY IT FREE
              </button>
            </div>
          </div>

          {/* Right Side - Enhanced Dashboard with Theme Colors */}
          <div className="md:col-span-6 relative bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a] rounded-2xl p-8 text-white max-w-md mx-auto md:mx-0 shadow-2xl border border-[#2a5a3a]/30">

            {/* Enhanced Overview Section */}
            <div className="mb-8">
              <h3 className="text-xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-200">Overview</h3>

              <div className="flex justify-between text-sm mb-3">
                <span className="text-[#2a5a3a] font-bold bg-[#2a5a3a]/20 px-3 py-1 rounded-full">+ 12%</span>
                <span className="text-[#0a2e2a] font-bold bg-[#0a2e2a]/20 px-3 py-1 rounded-full">+ 4.8%</span>
              </div>

              <div className="flex justify-between font-bold text-4xl mb-3 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-200">
                <span>$450m</span>
                <span>8.901</span>
              </div>

              <div className="flex justify-between text-sm text-gray-300">
                <span>Total Income</span>
                <span>Total Sales</span>
              </div>
            </div>

            {/* Enhanced Report Section */}
            <div className="bg-gradient-to-r from-[#0a1e3a] to-[#0a2e2a] rounded-xl flex items-center justify-between px-6 py-4 shadow-xl border border-[#2a5a3a]/30 hover:border-[#2a5a3a]/50 transition-all duration-300">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-[#2a5a3a]/30 rounded-full flex items-center justify-center">
                  <i className="fas fa-chart-bar text-[#2a5a3a]"></i>
                </div>
                <div>
                  <span className="text-sm font-semibold">2024</span>
                  <div className="text-xs text-gray-300">Report Overview</div>
                </div>
              </div>
              <button className="bg-gradient-to-r from-[#2a5a3a] to-[#0a2e2a] hover:from-[#0a2e2a] hover:to-[#2a5a3a] rounded-xl w-12 h-12 flex items-center justify-center text-white shadow-lg transition-all duration-300 hover:scale-110">
                <i className="fas fa-arrow-right"></i>
              </button>
            </div>

            {/* Decorative Elements */}
            <div className="absolute top-4 right-4 w-4 h-4 bg-[#2a5a3a] rounded-full animate-pulse opacity-60"></div>
            <div className="absolute bottom-4 left-4 w-3 h-3 bg-[#0a2e2a] rounded-full animate-pulse opacity-60"></div>

          </div>

        </section>

        {/* PROFESSIONAL COMPANY LOGOS SECTION */}
        <section className="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-16">
          <div className="text-center mb-12">
            <h3 className="text-lg font-semibold text-[#7a7a7a] mb-8">Trusted by Industry Leaders</h3>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-8 lg:gap-16 opacity-70 hover:opacity-100 transition-opacity duration-300">
            <img alt="FedEx Logistics company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/9a5510d9-5102-4789-77bf-391b5a7a6a8c.jpg" width="144"/>
            <img alt="Crane Worldwide Logistics company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/0f431638-d78a-4551-cd41-7644bfec8fae.jpg" width="144"/>
            <img alt="Bollore Logistics company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/4819675f-e061-4a06-2144-05b136d669e9.jpg" width="144"/>
            <img alt="CEVA Logistics company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/c734b295-1592-409b-e4a1-f0fbf795847a.jpg" width="144"/>
            <img alt="HAVI company logo" className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110" height="48" loading="lazy" src="https://storage.googleapis.com/a1aa/image/bf5a67af-3d7f-42dc-2374-c55cdec36f40.jpg" width="144"/>
          </div>
        </section>

        {/* ENHANCED MOBILE APP SHOWCASE WITH THEME COLORS */}
        <section className="relative bg-gradient-to-br from-[#0a1e3a] via-[#0a2e2a] to-[#2a5a3a] max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-24 rounded-t-3xl shadow-2xl border-t-4 border-[#2a5a3a]">

          {/* Enhanced Floating Arrow Icon */}
          <div className="absolute -top-8 left-1/2 -translate-x-1/2 w-16 h-16 rounded-full bg-gradient-to-r from-[#2a5a3a] to-[#0a2e2a] flex items-center justify-center text-white text-xl shadow-2xl animate-bounce border-4 border-white/20">
            <i className="fas fa-arrow-down"></i>
          </div>

          {/* Background Decorative Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 left-20 w-32 h-32 border-2 border-[#2a5a3a]/20 rounded-full animate-pulse"></div>
            <div className="absolute bottom-20 right-20 w-24 h-24 border-2 border-[#0a2e2a]/20 rounded-full animate-pulse"></div>
            <div className="absolute top-1/2 right-1/4 w-16 h-16 border-2 border-[#0a1e3a]/20 rounded-full animate-pulse"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center relative z-10">

            {/* Left Side - Enhanced Mobile Mockup */}
            <div className="relative max-w-xs mx-auto lg:mx-0">

              {/* Enhanced Clock Icon */}
              <div className="absolute top-5 left-5 w-12 h-12 rounded-full bg-gradient-to-r from-[#2a5a3a]/30 to-[#0a2e2a]/30 backdrop-blur-md flex items-center justify-center text-white text-lg shadow-xl z-10 border border-white/20">
                <i className="fas fa-clock"></i>
              </div>

              {/* Mobile Screen with Enhanced Border */}
              <div className="relative">
                <img alt="Mobile phone showing warehouse management app interface" className="rounded-3xl shadow-2xl border-4 border-[#2a5a3a]/30" height="560" src="https://storage.googleapis.com/a1aa/image/1347993e-5ce4-4703-cd22-1a5752fe0d69.jpg" width="280"/>
                <div className="absolute inset-0 rounded-3xl bg-gradient-to-t from-[#0a1e3a]/20 to-transparent pointer-events-none"></div>
              </div>

              {/* Enhanced Floating Badge */}
              <div className="absolute bottom-14 right-14 w-24 h-24 rounded-full border-2 border-[#2a5a3a]/40 bg-gradient-to-r from-[#0a1e3a]/40 to-[#0a2e2a]/40 backdrop-blur-md text-white/80 text-xs flex items-center justify-center text-center p-3 shadow-2xl hover:scale-110 transition-transform duration-300">
                <span className="font-medium leading-tight">Warehouse Management Software</span>
              </div>

              {/* Enhanced Arrow Down Icon */}
              <div className="absolute bottom-28 right-20 w-12 h-12 rounded-full bg-gradient-to-r from-[#2a5a3a]/30 to-[#0a2e2a]/30 backdrop-blur-md flex items-center justify-center text-white text-lg shadow-xl border border-white/20 hover:scale-110 transition-transform duration-300">
                <i className="fas fa-arrow-down"></i>
              </div>

              {/* Additional Decorative Elements */}
              <div className="absolute top-20 right-5 w-6 h-6 bg-[#2a5a3a]/40 rounded-full animate-pulse"></div>
              <div className="absolute bottom-40 left-5 w-4 h-4 bg-[#0a2e2a]/40 rounded-full animate-pulse"></div>

            </div>

            {/* Right Side - Enhanced Content */}
            <div className="text-white">
              <h3 className="text-4xl font-bold mb-8 leading-tight text-transparent bg-clip-text bg-gradient-to-r from-white via-gray-100 to-[#2a5a3a]">
                We have a solution for eCommerce challenges of all shapes & sizes across Asia Pacific
              </h3>

              <p className="mb-6 text-gray-200 leading-relaxed text-lg">
                Whether you are an eCommerce distributor, an asset-light enabler, an offline retailer transforming to omni-channel, a B2B logistics company trying to start eCommerce fulfillment, or an SME scaling online business, you have your own unique set of challenges.
              </p>

              <p className="mb-8 text-gray-200 leading-relaxed text-lg">
                Flexe's cutting-edge technology simplifies backend operations, and helps you achieve unprecedented productivity through automated selling management.
              </p>

              <div className="flex flex-col sm:flex-row gap-6">
                <button className="bg-gradient-to-r from-[#2a5a3a] to-[#0a2e2a] hover:from-[#0a2e2a] hover:to-[#2a5a3a] text-white font-semibold text-lg rounded-full px-10 py-4 transition-all duration-500 hover:scale-105 shadow-2xl hover:shadow-3xl border border-[#2a5a3a]/30">
                  GET STARTED
                </button>
                <button className="border-2 border-[#2a5a3a]/60 bg-[#0a1e3a]/30 backdrop-blur-sm text-white font-semibold text-lg rounded-full px-10 py-4 hover:bg-[#2a5a3a]/20 hover:border-[#2a5a3a] transition-all duration-500 hover:scale-105">
                  TRY IT FREE
                </button>
              </div>
            </div>

          </div>

        </section>

        {/* PROFESSIONAL DISCOVER SECTION */}
        <section className="max-w-7xl mx-auto px-6 sm:px-10 lg:px-16 py-24 text-center">

          <h2 className="text-3xl font-bold text-[#0a1e3a] mb-16">
            Discover Who We Are
          </h2>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-5xl mx-auto mb-20">

            {/* Features */}
            <div className="group hover:transform hover:scale-105 transition-all duration-300">
              <div className="mx-auto mb-6 w-16 h-16 rounded-full bg-[#0a1e3a] flex items-center justify-center text-[#f97316] text-2xl shadow-lg group-hover:shadow-xl transition-all duration-300">
                <i className="fas fa-cogs"></i>
              </div>
              <h4 className="text-lg font-semibold text-[#0a1e3a] mb-4">Features</h4>
              <p className="text-sm text-[#7a7a7a] leading-relaxed">
                Ensure maximum output from your warehouse to achieve rising success on both B2B and B2C fronts with Flexe's smart & intuitive features.
              </p>
            </div>

            {/* Industry Solutions */}
            <div className="group hover:transform hover:scale-105 transition-all duration-300">
              <div className="mx-auto mb-6 w-16 h-16 rounded-full bg-[#0a1e3a] flex items-center justify-center text-[#f97316] text-2xl shadow-lg group-hover:shadow-xl transition-all duration-300">
                <i className="fas fa-industry"></i>
              </div>
              <h4 className="text-lg font-semibold text-[#0a1e3a] mb-4">Industry Solutions</h4>
              <p className="text-sm text-[#7a7a7a] leading-relaxed">
                Discover how you can start eCommerce fulfillment, streamline warehouse operations, manage logistics in-house, or solve your logistics challenges.
              </p>
            </div>

            {/* Case Studies */}
            <div className="group hover:transform hover:scale-105 transition-all duration-300">
              <div className="mx-auto mb-6 w-16 h-16 rounded-full bg-[#0a1e3a] flex items-center justify-center text-[#f97316] text-2xl shadow-lg group-hover:shadow-xl transition-all duration-300">
                <i className="fas fa-file-alt"></i>
              </div>
              <h4 className="text-lg font-semibold text-[#0a1e3a] mb-4">Case Studies</h4>
              <p className="text-sm text-[#7a7a7a] leading-relaxed">
                Find out how multiple global players such as Nestle, Luxasia, Zilingo & more have defined their transformational journeys with Flexe.
              </p>
            </div>

          </div>

          {/* Divider */}
          <hr className="border-t border-[#e3e8ef] mb-16"/>

          {/* Professional Testimonial Section */}
          <div className="max-w-2xl mx-auto">
            <div className="w-20 h-20 rounded-full bg-[#0a1e3a] mx-auto mb-8 flex items-center justify-center text-[#f97316] text-3xl shadow-lg">
              <i className="fas fa-quote-left"></i>
            </div>

            <blockquote className="text-xl text-[#0a1e3a] mb-6 leading-relaxed font-medium">
              "We found the Inventory app to be a powerful tool to better organize our warehouse. It's simple and easily customizable."
            </blockquote>

            <p className="text-[#f97316] font-semibold mb-12">
              Mario Riva, COO
            </p>

            {/* Navigation Buttons */}
            <div className="flex justify-center space-x-8">
              <button className="w-12 h-12 rounded-full border-2 border-[#0a1e3a] text-[#0a1e3a] flex items-center justify-center hover:bg-[#0a1e3a] hover:text-white transition-all duration-300 shadow-md">
                <i className="fas fa-arrow-left"></i>
              </button>
              <button className="w-12 h-12 rounded-full border-2 border-[#0a1e3a] text-[#0a1e3a] flex items-center justify-center hover:bg-[#0a1e3a] hover:text-white transition-all duration-300 shadow-md">
                <i className="fas fa-arrow-right"></i>
              </button>
            </div>
          </div>

        </section>

        <Footer />
      </div>
    </>
  );
}
