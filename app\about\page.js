'use client';

import { useState } from 'react';
import Image from "next/image";
import Header from '../components/Header';

export default function AboutPage() {
  const [selectedLeader, setSelectedLeader] = useState(null);

  const leaders = [
    {
      id: 1,
      name: "<PERSON>",
      title: "Co-founder & CEO",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "<PERSON> is a seasoned technology executive, with leadership experience in both startups and large, global corporations. Prior to co-founding Flexe, <PERSON> was CEO of AdReady, a Seattle-based advertising technology company."
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      title: "Chief Financial Officer",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "<PERSON><PERSON> is the Chief Financial Officer at Flexe. Prior to joining Flexe, he served as CFO for Icertis and Chef Software, as well as various finance, operations and marketing leadership roles at Microsoft and Amazon."
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON>",
      title: "Chief Product and Technology Officer",
      image: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Pirasenna is the Chief Product and Technology Officer at Flexe. Prior to joining Flexe, he served as the Chief Technology Officer for ServUs, as well as various technology, digital, and engineering roles."
    },
    {
      id: 4,
      name: "Katie Carter",
      title: "VP, Sales",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Katie is the VP of Sales at Flexe. Prior to joining Flexe, she served in business development roles at Crowley Maritime. She holds a Communications degree from Florida State University."
    },
    {
      id: 5,
      name: "Ben Cooke",
      title: "VP, Revenue Operations",
      image: "https://images.unsplash.com/photo-**********-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Ben Cooke is the VP of Revenue Operations at Flexe. Prior to joining Flexe, he served various revenue operations roles at Florence Healthcare, Bolt, and Convoy Inc."
    },
    {
      id: 6,
      name: "Holly Ann Walker",
      title: "VP, People",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Holly is VP of People at Flexe. Prior to joining Flexe, she held human capital roles at Smartsheet, Zulily and Geocaching. She holds a bachelor's degree in Arabic Language and Middle Eastern Studies."
    }
  ];

  return (
    <div>
      <Header currentPage="about" />

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img alt="Modern shipping containers and logistics" className="w-full h-full object-cover opacity-20" src="https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"/>
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-800"></div>
        </div>

        <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white leading-tight mb-8">
            To create the open logistics network that optimizes the
            <span className="block text-[#E6B24B]">global delivery of goods.</span>
          </h1>
        </div>
      </section>

      {/* About Us Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <div className="inline-flex items-center px-4 py-2 bg-[#E6B24B]/10 border border-[#E6B24B]/30 rounded-full mb-8">
                <span className="text-[#E6B24B] font-semibold text-sm uppercase tracking-wide">About Us</span>
              </div>

              <p className="text-xl text-gray-300 leading-relaxed mb-8">
                <strong className="text-white">Flexe provides Flexible Warehousing Infrastructure:</strong> tech-enabled warehouse services that allow enterprises to evolve, optimize and prepare networks for long-term strategic growth.
              </p>

              <p className="text-lg text-gray-300 leading-relaxed">
                A single technology integration opens access to 700+ warehouse operators across the U.S. and Canada. Founded in 2013 and headquartered in Seattle, Flexe brings deep logistics expertise and enterprise-grade technology to deliver innovative eCommerce fulfillment, retail distribution and network capacity solutions to Fortune 500 enterprises.
              </p>
            </div>

            <div className="relative">
              <img alt="Modern logistics and shipping operations" className="w-full h-96 object-cover rounded-2xl" src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"/>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-6">Flexe Values</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8">
              <div className="w-16 h-16 bg-[#E6B24B]/20 rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-bullseye text-[#E6B24B] text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Deliver Customer Impact</h3>
              <p className="text-gray-300 leading-relaxed">
                Customers' success is our success. We build strong relationships with both our internal and external customers, deeply understand their needs, and deliver solutions that drive valuable impact.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8">
              <div className="w-16 h-16 bg-[#E6B24B]/20 rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-brain text-[#E6B24B] text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Think Critically</h3>
              <p className="text-gray-300 leading-relaxed">
                Disrupting the Logistics industry is a puzzle full of complexities and challenges. We solve the right problems by demonstrating curiosity, being data-driven, and understanding root causes.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8">
              <div className="w-16 h-16 bg-[#E6B24B]/20 rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-users text-[#E6B24B] text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Work Together</h3>
              <p className="text-gray-300 leading-relaxed">
                We make each other better, regardless of tenure or title. We value diversity of thought and experience, trust others, and show respect through direct feedback and collaboration.
              </p>
            </div>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-[#E6B24B]/20 rounded-2xl p-8">
              <div className="w-16 h-16 bg-[#E6B24B]/20 rounded-xl flex items-center justify-center mb-6">
                <i className="fas fa-shield-alt text-[#E6B24B] text-2xl"></i>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Be Accountable</h3>
              <p className="text-gray-300 leading-relaxed">
                Trust is earned or lost in every action. We are not perfect, but we take ownership of our decisions and actions, holding ourselves and others accountable to deliver on our commitments with transparency and integrity.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Leadership Team Section */}
      <section className="py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-[#E6B24B]/10 border border-[#E6B24B]/30 rounded-full mb-6">
              <i className="fas fa-users text-[#E6B24B] mr-2"></i>
              <span className="text-[#E6B24B] font-semibold text-sm uppercase tracking-wide">Leadership Team</span>
            </div>
            <h2 className="text-4xl font-bold text-white mb-6">Flexe Leadership Team</h2>
            <img alt="Team collaboration in modern office" className="w-full h-64 object-cover rounded-2xl mb-8" src="https://images.unsplash.com/photo-*************-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"/>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
            {leaders.map((leader) => (
              <div
                key={leader.id}
                className="text-center cursor-pointer group"
                onClick={() => setSelectedLeader(leader)}
              >
                <div className="relative mb-4 overflow-hidden rounded-2xl">
                  <img
                    alt={leader.name}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    src={leader.image}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <h3 className="text-white font-semibold mb-1 group-hover:text-[#E6B24B] transition-colors duration-300">{leader.name}</h3>
                <p className="text-gray-400 text-sm">{leader.title}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Team CTA */}
      <section className="py-20 bg-gradient-to-r from-[#E6B24B]/10 to-transparent border-t border-[#E6B24B]/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Interested in joining the Flexe team?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Flexe is disrupting the $1.6 trillion logistics & supply chain industry. Join our fast-growing team of people dedicated to making a difference. If you are passionate about changing the way companies do business, working with amazing people, and truly owning your work, take a look at our open positions and apply today.
          </p>
          <button className="bg-gradient-to-r from-[#E6B24B] to-[#D4A843] text-black font-bold px-10 py-5 rounded-xl text-lg hover:scale-105 transition-transform duration-300 shadow-lg">
            Open Positions
          </button>
        </div>
      </section>

      {/* Leader Modal */}
      {selectedLeader && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4" onClick={() => setSelectedLeader(null)}>
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 max-w-2xl w-full border border-[#E6B24B]/20" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-start space-x-6 mb-6">
              <img
                alt={selectedLeader.name}
                className="w-24 h-24 object-cover rounded-xl"
                src={selectedLeader.image}
              />
              <div>
                <h3 className="text-2xl font-bold text-white mb-2">{selectedLeader.name}</h3>
                <p className="text-[#E6B24B] font-semibold">{selectedLeader.title}</p>
              </div>
              <button
                onClick={() => setSelectedLeader(null)}
                className="ml-auto text-gray-400 hover:text-white transition-colors duration-300"
              >
                <i className="fas fa-times text-xl"></i>
              </button>
            </div>
            <p className="text-gray-300 leading-relaxed">{selectedLeader.bio}</p>
          </div>
        </div>
      )}

      {/* Footer */}
      <footer className="relative bg-gradient-to-t from-black via-gray-900 to-black border-t border-[#E6B24B]/20 text-white">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-12">
            {/* Footer Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 sm:space-x-3 group mb-6">
                <div className="w-10 h-10 sm:w-12 sm:h-12 gradient-bg rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <i className="fas fa-warehouse text-black text-lg sm:text-xl"></i>
                </div>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white group-hover:text-[#E6B24B] transition-colors duration-300">Flexe</h2>
                  <p className="text-xs text-gray-300 font-medium">Professional Warehousing</p>
                </div>
              </div>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed">
                Transform your supply chain with flexible warehousing infrastructure that scales with your business needs and drives sustainable growth.
              </p>
              <div className="text-gray-300 space-y-2">
                <p>4786 1st Ave S, Suite #106</p>
                <p>Seattle, WA 98134</p>
                <p>
                  <a href="mailto:<EMAIL>" className="hover:text-[#E6B24B] transition-colors duration-300"><EMAIL></a>
                  {" | "}
                  <a href="tel:************" className="hover:text-[#E6B24B] transition-colors duration-300">************</a>
                </p>
              </div>
            </div>

            {/* Company */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Company</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/about">About Us</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/careers">Careers</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact</a></li>
              </ul>
            </div>

            {/* Solutions */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Solutions</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Warehousing</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Fulfillment</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/solutions">Distribution</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h6 className="font-bold mb-6 text-[#E6B24B] text-sm uppercase tracking-wider">Support</h6>
              <ul className="space-y-4">
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/resources">Resources</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Help Center</a></li>
                <li><a className="text-gray-300 hover:text-[#E6B24B] transition-colors duration-300" href="/contact">Contact Support</a></li>
              </ul>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-[#E6B24B]/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 Flexe. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm">
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Privacy Policy</a>
                <a className="text-gray-400 hover:text-[#E6B24B] transition-colors duration-300" href="#">Terms of Service</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
